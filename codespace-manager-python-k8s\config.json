{"ai": {"provider": "openai", "apiKey": "974fd8d1c155aa3d04b17bf253176b5e", "model": "qwen2.5-32b-instruct", "baseUrl": "https://gateway.chat.sensedeal.vip/v1"}, "kubernetes": {"apiServer": "http://*************:8001/api/v1", "namespace": "codespace", "defaultImage": "python:bullseye", "portRange": {"min": 30001, "max": 32767}, "resources": {"requests": {"cpu": "100m", "memory": "256Mi"}, "limits": {"cpu": "1000m", "memory": "2Gi"}}, "volumes": {"workspace": "/home/<USER>", "tmp": "/tmp"}}, "paths": {"data": "./data", "k8s": "./k8s"}, "file_operations": {"max_file_size": "100MB", "supported_encodings": ["utf-8", "gbk", "ascii"], "chunk_size": 8192, "backup_enabled": true}, "security": {"bcrypt_rounds": 12, "min_password_length": 6, "allowed_file_extensions": [".py", ".js", ".java", ".cpp", ".c", ".sh", ".txt", ".md", ".json", ".yaml", ".yml"], "forbidden_paths": ["/etc", "/usr", "/bin", "/sbin", "/root"]}}