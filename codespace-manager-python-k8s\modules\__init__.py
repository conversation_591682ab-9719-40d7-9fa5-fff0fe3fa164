# CodeSpace Manager - Kubernetes 版本模块包
# 
# 本包包含所有核心功能模块：
# - user_manager: 用户管理
# - k8s_manager: Kubernetes 管理
# - file_manager: 文件管理
# - code_executor: 代码执行
# - ai_manager: AI助手

__version__ = "1.0.0"
__author__ = "CodeSpace Team"

# 导入所有模块
from .user_manager import UserManager
from .k8s_manager import K8sManager
from .file_manager import FileManager
from .code_executor import CodeExecutor
from .ai_manager import AIManager

__all__ = [
    'UserManager',
    'K8sManager', 
    'FileManager',
    'CodeExecutor',
    'AIManager'
]
