apiVersion: v1
kind: Pod
metadata:
  name: codespace-{user_id}
  namespace: {namespace}
  labels:
    app: codespace
    user: {user_id}
    created-by: codespace-manager-k8s
spec:
  containers:
  - name: workspace
    image: {image}
    ports:
    - containerPort: 8080
      name: http
    env:
    - name: USER_ID
      value: "{user_id}"
    - name: WORKSPACE_DIR
      value: "/home/<USER>"
    - name: PYTHONUNBUFFERED
      value: "1"
    volumeMounts:
    - name: workspace-volume
      mountPath: /home/<USER>
    - name: tmp-volume
      mountPath: /tmp
    resources:
      requests:
        cpu: {cpu_request}
        memory: {memory_request}
      limits:
        cpu: {cpu_limit}
        memory: {memory_limit}
    command: ["/bin/bash"]
    args: 
    - "-c"
    - |
      # 创建工作目录
      mkdir -p /home/<USER>
      cd /home/<USER>
      
      # 安装基本工具
      apt-get update && apt-get install -y \
        curl \
        wget \
        git \
        vim \
        nano \
        tree \
        htop \
        python3-pip \
        nodejs \
        npm \
        default-jdk \
        gcc \
        g++ \
        make \
        build-essential
      
      # 安装Python包
      pip3 install --upgrade pip
      pip3 install requests flask fastapi jupyter notebook
      
      # 创建示例文件
      cat > hello.py << 'EOF'
      #!/usr/bin/env python3
      print("Hello from CodeSpace Manager!")
      print("This is your personal development environment.")
      print("You can create, edit, and run code files here.")
      EOF
      
      cat > hello.js << 'EOF'
      console.log("Hello from Node.js!");
      console.log("Welcome to your CodeSpace environment!");
      EOF
      
      cat > README.md << 'EOF'
      # Welcome to Your CodeSpace!
      
      This is your personal development environment running in Kubernetes.
      
      ## Available Tools
      - Python 3 with pip
      - Node.js with npm
      - Java Development Kit
      - GCC/G++ for C/C++
      - Git for version control
      - Various text editors (vim, nano)
      
      ## Getting Started
      1. Create your code files
      2. Use the CodeSpace Manager to run them
      3. Install additional packages as needed
      
      Happy coding!
      EOF
      
      # 设置权限
      chmod +x hello.py
      chmod 755 /home/<USER>
      
      # 启动HTTP服务器
      echo "Starting development environment..."
      python3 -m http.server 8080
    workingDir: /home/<USER>
  volumes:
  - name: workspace-volume
    emptyDir: {}
  - name: tmp-volume
    emptyDir: {}
  restartPolicy: Always
