#!/usr/bin/env python3
"""
CodeSpace Manager Web 应用

提供 Web 界面来管理 Kubernetes 中的开发环境
"""

import sys
import os
import json
from pathlib import Path
from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from flask_socketio import Socket<PERSON>, emit
from werkzeug.security import check_password_hash, generate_password_hash

# 添加模块路径
sys.path.append(str(Path(__file__).parent / 'modules'))

from user_manager import UserManager
from k8s_manager import K8sManager
from file_manager import FileManager
from code_executor import CodeExecutor
from ai_manager import AIManager

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-this'
socketio = SocketIO(app, cors_allowed_origins="*")

# 初始化管理器
user_manager = UserManager()
k8s_manager = K8sManager()
file_manager = FileManager(k8s_manager)
code_executor = CodeExecutor(k8s_manager)
ai_manager = AIManager()


@app.route('/')
def index():
    """主页"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('index.html')


@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        try:
            result = user_manager.login_user(username, password)
            if result['success']:
                session['user_id'] = result['user']['id']
                session['username'] = result['user']['username']
                session['namespace'] = result['user'].get('namespace')
                
                # 确保用户有Pod
                ensure_user_pod(result['user'])
                
                flash('登录成功！', 'success')
                return redirect(url_for('index'))
            else:
                flash('登录失败：用户名或密码错误', 'error')
        except Exception as e:
            flash(f'登录失败：{str(e)}', 'error')
    
    return render_template('login.html')


@app.route('/register', methods=['GET', 'POST'])
def register():
    """注册页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        
        if password != confirm_password:
            flash('密码不匹配', 'error')
            return render_template('register.html')
        
        try:
            result = user_manager.register_user(username, password)
            if result['success']:
                flash('注册成功！请登录', 'success')
                return redirect(url_for('login'))
        except Exception as e:
            flash(f'注册失败：{str(e)}', 'error')
    
    return render_template('register.html')


@app.route('/logout')
def logout():
    """退出登录"""
    session.clear()
    flash('已退出登录', 'info')
    return redirect(url_for('login'))


@app.route('/files')
def files():
    """文件管理页面"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('files.html')


@app.route('/editor')
def editor():
    """代码编辑器页面"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('editor.html')


@app.route('/terminal')
def terminal():
    """终端页面"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('terminal.html')


@app.route('/ai')
def ai():
    """AI助手页面"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('ai.html')


# API 路由
@app.route('/api/files/list')
def api_list_files():
    """列出文件API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '未登录'})
    
    path = request.args.get('path', '/home/<USER>')
    result = file_manager.list_files(session['user_id'], path)
    return jsonify(result)


@app.route('/api/files/read')
def api_read_file():
    """读取文件API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '未登录'})
    
    file_path = request.args.get('path')
    if not file_path:
        return jsonify({'success': False, 'error': '缺少文件路径'})
    
    result = file_manager.read_file(session['user_id'], file_path)
    return jsonify(result)


@app.route('/api/files/write', methods=['POST'])
def api_write_file():
    """写入文件API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '未登录'})
    
    data = request.get_json()
    file_path = data.get('path')
    content = data.get('content')
    
    if not file_path or content is None:
        return jsonify({'success': False, 'error': '缺少文件路径或内容'})
    
    result = file_manager.write_file(session['user_id'], file_path, content)
    return jsonify(result)


@app.route('/api/files/delete', methods=['POST'])
def api_delete_file():
    """删除文件API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '未登录'})
    
    data = request.get_json()
    file_path = data.get('path')
    
    if not file_path:
        return jsonify({'success': False, 'error': '缺少文件路径'})
    
    result = file_manager.delete_file(session['user_id'], file_path)
    return jsonify(result)


@app.route('/api/files/replace', methods=['POST'])
def api_replace_content():
    """替换文件内容API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '未登录'})
    
    data = request.get_json()
    file_path = data.get('path')
    old_content = data.get('old_content')
    new_content = data.get('new_content')
    regex = data.get('regex', False)
    global_replace = data.get('global_replace', False)
    
    if not file_path or old_content is None or new_content is None:
        return jsonify({'success': False, 'error': '缺少必要参数'})
    
    result = file_manager.replace_file_content(
        session['user_id'], file_path, old_content, new_content, regex, global_replace
    )
    return jsonify(result)


@app.route('/api/execute', methods=['POST'])
def api_execute():
    """执行命令API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '未登录'})
    
    data = request.get_json()
    command = data.get('command')
    working_dir = data.get('working_dir', '/home/<USER>')
    
    if not command:
        return jsonify({'success': False, 'error': '缺少命令'})
    
    result = code_executor.execute_command(session['user_id'], command, working_dir)
    return jsonify(result)


@app.route('/api/ai/chat', methods=['POST'])
def api_ai_chat():
    """AI对话API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '未登录'})
    
    data = request.get_json()
    message = data.get('message')
    
    if not message:
        return jsonify({'success': False, 'error': '缺少消息内容'})
    
    result = ai_manager.generate_response(message)
    return jsonify(result)


@app.route('/api/ai/generate', methods=['POST'])
def api_ai_generate():
    """AI代码生成API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '未登录'})
    
    data = request.get_json()
    description = data.get('description')
    language = data.get('language', 'python')
    
    if not description:
        return jsonify({'success': False, 'error': '缺少描述'})
    
    result = ai_manager.generate_code_from_description(description, language)
    return jsonify(result)


@app.route('/api/pod/status')
def api_pod_status():
    """获取Pod状态API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': '未登录'})
    
    pod_info = k8s_manager.get_user_pod(session['user_id'])
    if not pod_info:
        return jsonify({'success': False, 'error': 'Pod不存在'})
    
    status = k8s_manager.get_pod_status(pod_info['podName'], pod_info['namespace'])
    return jsonify({
        'success': True,
        'pod_info': pod_info,
        'status': status
    })


def ensure_user_pod(user):
    """确保用户有可用的Pod"""
    try:
        existing_pod = k8s_manager.get_user_pod(user['id'])
        
        if existing_pod:
            pod_status = k8s_manager.check_pod_status(
                existing_pod['podName'], 
                existing_pod['namespace']
            )
            if pod_status:
                return existing_pod
        
        # 创建新Pod
        pod_info = k8s_manager.create_user_pod(
            user['id'],
            user['username'],
            user.get('namespace')
        )
        
        if pod_info:
            user_manager.update_user_pod(user['username'], pod_info['podId'])
        
        return pod_info
    except Exception as e:
        print(f"确保用户Pod失败: {e}")
        return None


if __name__ == '__main__':
    # 创建模板目录
    templates_dir = Path(__file__).parent / 'templates'
    templates_dir.mkdir(exist_ok=True)
    
    static_dir = Path(__file__).parent / 'static'
    static_dir.mkdir(exist_ok=True)
    
    print("启动 CodeSpace Manager Web 应用...")
    print("访问地址: http://localhost:5000")
    
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
