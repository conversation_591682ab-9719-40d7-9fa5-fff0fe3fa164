import json
import uuid
import bcrypt
import os
from pathlib import Path
from datetime import datetime


class UserManager:
    def __init__(self):
        self.users_file = Path.cwd() / 'data' / 'users.json'
        self.ensure_data_directory()

    def ensure_data_directory(self):
        """确保数据目录存在"""
        data_dir = self.users_file.parent
        data_dir.mkdir(exist_ok=True)
        
        # 如果用户文件不存在，创建空的用户数据
        if not self.users_file.exists():
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump({}, f)

    def load_users(self):
        """加载用户数据"""
        try:
            with open(self.users_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as error:
            print(f'Error loading users: {error}')
            return {}

    def save_users(self, users):
        """保存用户数据"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(users, f, indent=2, ensure_ascii=False)
        except Exception as error:
            print(f'Error saving users: {error}')
            raise error

    def register_user(self, username, password):
        """注册新用户"""
        try:
            users = self.load_users()
            
            # 检查用户名是否已存在
            for user_id, user_data in users.items():
                if user_data['username'] == username:
                    raise Exception(f'用户名 {username} 已存在')
            
            # 生成用户ID
            user_id = str(uuid.uuid4())
            
            # 加密密码
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
            
            # 创建用户数据
            user_data = {
                'id': user_id,
                'username': username,
                'passwordHash': password_hash.decode('utf-8'),
                'createdAt': datetime.now().isoformat(),
                'lastLoginAt': None,
                'podId': None,
                'namespace': f'user-{user_id[:8]}'  # 为每个用户创建独立的命名空间
            }
            
            # 保存用户
            users[user_id] = user_data
            self.save_users(users)
            
            return {
                'success': True,
                'userId': user_id,
                'username': username,
                'namespace': user_data['namespace']
            }
        except Exception as error:
            raise Exception(f'注册失败: {error}')

    def login_user(self, username, password):
        """用户登录"""
        try:
            users = self.load_users()
            
            # 查找用户
            user_data = None
            for user_id, data in users.items():
                if data['username'] == username:
                    user_data = data
                    break
            
            if not user_data:
                raise Exception(f'用户 {username} 不存在')
            
            # 验证密码
            if not bcrypt.checkpw(password.encode('utf-8'), user_data['passwordHash'].encode('utf-8')):
                raise Exception('密码错误')
            
            # 更新最后登录时间
            user_data['lastLoginAt'] = datetime.now().isoformat()
            users[user_data['id']] = user_data
            self.save_users(users)
            
            return {
                'success': True,
                'user': user_data
            }
        except Exception as error:
            raise Exception(f'登录失败: {error}')

    def get_user_by_id(self, user_id):
        """根据ID获取用户"""
        try:
            users = self.load_users()
            return users.get(user_id)
        except Exception as error:
            print(f'Error getting user: {error}')
            return None

    def update_user_pod(self, username, pod_id):
        """更新用户的Pod ID"""
        try:
            users = self.load_users()
            
            # 查找用户
            for user_id, user_data in users.items():
                if user_data['username'] == username:
                    user_data['podId'] = pod_id
                    users[user_id] = user_data
                    self.save_users(users)
                    return True
            
            return False
        except Exception as error:
            print(f'Error updating user pod: {error}')
            return False

    def list_users(self):
        """列出所有用户"""
        try:
            users = self.load_users()
            user_list = []
            
            for user_id, user_data in users.items():
                user_list.append({
                    'id': user_id,
                    'username': user_data['username'],
                    'createdAt': user_data['createdAt'],
                    'lastLoginAt': user_data.get('lastLoginAt'),
                    'podId': user_data.get('podId'),
                    'namespace': user_data.get('namespace')
                })
            
            return user_list
        except Exception as error:
            print(f'Error listing users: {error}')
            return []

    def delete_user(self, user_id):
        """删除用户"""
        try:
            users = self.load_users()
            
            if user_id in users:
                del users[user_id]
                self.save_users(users)
                return True
            
            return False
        except Exception as error:
            print(f'Error deleting user: {error}')
            return False

    def change_password(self, user_id, old_password, new_password):
        """修改密码"""
        try:
            users = self.load_users()
            
            if user_id not in users:
                raise Exception('用户不存在')
            
            user_data = users[user_id]
            
            # 验证旧密码
            if not bcrypt.checkpw(old_password.encode('utf-8'), user_data['passwordHash'].encode('utf-8')):
                raise Exception('旧密码错误')
            
            # 加密新密码
            new_password_hash = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt())
            user_data['passwordHash'] = new_password_hash.decode('utf-8')
            
            # 保存更新
            users[user_id] = user_data
            self.save_users(users)
            
            return True
        except Exception as error:
            print(f'Error changing password: {error}')
            return False
