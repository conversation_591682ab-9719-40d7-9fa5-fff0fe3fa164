# 快速启动指南

## 🚀 5分钟快速体验

### 前置条件
- Python 3.7+ 已安装
- 可访问的 Kubernetes 集群（API Server: http://*************:8001/api/v1）

### 步骤 1: 安装依赖
```bash
cd codespace-manager-python-k8s
pip install -r requirements.txt
```

### 步骤 2: 运行测试
```bash
python test_basic.py
```
预期结果：4/5 测试通过（Kubernetes 连接可能失败，这是正常的）

### 步骤 3: 启动应用
```bash
python run.py
```

### 步骤 4: 注册和登录
1. 选择 "📝 注册"
2. 输入用户名和密码
3. 注册成功后选择 "🔐 登录"
4. 系统会自动创建您的专属 Pod

### 步骤 5: 开始使用
登录后您可以：
- 🤖 **AI助手**: 生成代码、分析代码、自由对话
- 📁 **文件管理**: 创建、编辑、替换文件内容
- ⚡ **代码执行**: 运行 Python、JavaScript、Java 等代码
- 📊 **Pod管理**: 查看和管理您的容器

## 🎯 核心功能演示

### AI 代码生成
1. 主菜单选择 "🤖 AI助手"
2. 选择 "📝 生成代码"
3. 描述功能：例如 "创建一个计算斐波那契数列的函数"
4. 选择语言：Python
5. AI 会生成代码，可选择保存到工作空间

### 文件内容替换
1. 主菜单选择 "📁 文件管理"
2. 选择 "🔄 替换内容"
3. 输入文件路径：`/home/<USER>/config.py`
4. 要替换的内容：`DEBUG = False`
5. 新内容：`DEBUG = True`
6. 选择是否使用正则表达式和全局替换

### 代码执行
1. 主菜单选择 "⚡ 代码执行"
2. 选择 "📄 运行文件"
3. 从列表中选择代码文件
4. 输入命令行参数（可选）
5. 查看执行结果

## 🔧 配置自定义

### 修改 Kubernetes 设置
编辑 `config.json`：
```json
{
  "kubernetes": {
    "apiServer": "http://YOUR_K8S_API:8001/api/v1",
    "defaultImage": "python:bullseye",
    "portRange": {"min": 30001, "max": 32767}
  }
}
```

### 调整资源限制
```json
{
  "kubernetes": {
    "resources": {
      "requests": {"cpu": "200m", "memory": "512Mi"},
      "limits": {"cpu": "2000m", "memory": "4Gi"}
    }
  }
}
```

### 文件操作设置
```json
{
  "file_operations": {
    "max_file_size": "200MB",
    "chunk_size": 16384,
    "backup_enabled": true
  }
}
```

## 🐛 常见问题

### Q: Kubernetes 连接失败
**A**: 检查以下项目：
- K8s 集群是否运行
- API Server 地址是否正确
- 网络连接是否正常
- 端口 8001 是否开放

### Q: Pod 创建失败
**A**: 可能的原因：
- 资源配额不足
- 镜像拉取失败
- 命名空间权限问题

### Q: 文件操作失败
**A**: 检查：
- 文件路径是否正确
- 文件大小是否超过限制
- Pod 是否正常运行

### Q: AI 功能不可用
**A**: 确认：
- 网络连接正常
- API 密钥配置正确
- AI 服务状态正常

## 📚 进阶使用

### 批量文件操作
1. 创建脚本文件
2. 使用代码执行功能运行脚本
3. 实现自动化文件处理

### 正则表达式替换
支持复杂的文本替换：
```regex
# 替换所有 IP 地址
模式: \d+\.\d+\.\d+\.\d+
替换: *************

# 替换函数名
模式: def old_function\(
替换: def new_function(
```

### 多语言开发
支持的语言和扩展名：
- Python: `.py`
- JavaScript: `.js`
- Java: `.java`
- C++: `.cpp`, `.cc`, `.cxx`
- C: `.c`
- Bash: `.sh`

## 🔒 安全注意事项

1. **不要在代码中硬编码敏感信息**
2. **定期备份重要文件**
3. **使用安全的文件路径**
4. **避免执行不信任的命令**
5. **定期更新依赖包**

## 📞 获取帮助

- 查看详细文档：`docs/` 目录
- 运行测试：`python test_basic.py`
- 查看错误日志：应用会显示详细错误信息
- 使用 AI 助手：在应用内询问技术问题

## 🎉 开始您的云原生开发之旅！

现在您已经准备好使用 CodeSpace Manager - Kubernetes 版本了。这个强大的工具将为您提供完整的云原生开发环境，支持多语言编程、智能文件管理和 AI 辅助开发。

祝您编程愉快！🚀
