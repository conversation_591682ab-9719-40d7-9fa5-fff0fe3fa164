# 用户使用指南

## 快速开始

### 1. 启动应用
```bash
python run.py
```

### 2. 注册账户
- 选择"📝 注册"
- 输入用户名和密码
- 系统会自动分配用户ID和命名空间

### 3. 登录系统
- 选择"🔐 登录"
- 输入用户名和密码
- 系统会自动创建您的专属 Pod

### 4. 开始使用
登录成功后，您将看到主菜单，可以选择不同的功能模块。

## 功能详解

### 🤖 AI助手

#### 自由对话
- 与AI进行自然语言对话
- 获取编程建议和技术支持
- 解答各种技术问题

#### 代码生成
- 描述您想要的功能
- 选择编程语言
- AI会生成相应的代码
- 可以直接保存到您的工作空间

#### 代码分析
- 选择工作空间中的代码文件
- AI会分析代码质量和潜在问题
- 提供改进建议和最佳实践

### 📁 文件管理

#### 列出文件
- 查看指定目录下的所有文件和文件夹
- 显示文件大小、权限等详细信息
- 支持递归浏览子目录

#### 查看文件
- 读取文件内容并显示
- 支持多种编码格式（UTF-8、GBK、ASCII）
- 自动检测文件编码

#### 编辑文件
- 创建新文件或编辑现有文件
- 使用系统默认编辑器
- 支持大文件编辑（最大100MB）

#### 替换内容
- 精确替换文件中的特定内容
- 支持正则表达式匹配
- 支持全局替换或单次替换
- 自动创建备份文件

#### 创建目录
- 在工作空间中创建新目录
- 支持递归创建多级目录

#### 删除文件
- 安全删除文件
- 自动创建备份（如果启用）
- 需要确认操作

#### 搜索文件
- 在文件内容中搜索特定文本
- 支持文件名模式匹配
- 显示匹配的文件列表

#### 文件信息
- 查看文件的详细信息
- 包括大小、创建时间、修改时间等

### ⚡ 代码执行

#### 执行命令
- 在 Pod 中执行任意命令
- 指定工作目录
- 实时显示输出和错误信息

#### 运行文件
- 自动检测文件类型
- 支持多种编程语言：
  - Python (.py)
  - JavaScript (.js)
  - Java (.java)
  - C++ (.cpp, .cc, .cxx)
  - C (.c)
  - Bash (.sh)
- 自动编译（如需要）
- 支持命令行参数

#### 安装包
- 使用不同的包管理器：
  - pip (Python)
  - npm (Node.js)
  - apt (系统包)
- 自动处理依赖关系

### 🔧 系统信息
- 查看 Pod 中的系统信息
- 检查已安装的编程语言和工具
- 显示系统资源使用情况

### 📊 Pod管理

#### Pod状态
- 查看当前用户 Pod 的详细状态
- 显示访问地址和端口信息
- 监控 Pod 运行状态

#### 列出所有Pod
- 查看系统中所有用户的 Pod
- 显示 Pod 状态和资源使用情况

## 最佳实践

### 文件组织
```
/home/<USER>/
├── projects/          # 项目目录
│   ├── project1/
│   └── project2/
├── scripts/           # 脚本文件
├── data/             # 数据文件
└── temp/             # 临时文件
```

### 代码开发流程
1. 使用AI助手生成初始代码
2. 在文件管理中创建和编辑文件
3. 使用代码执行功能测试代码
4. 利用AI助手分析和优化代码

### 安全注意事项
- 不要在代码中硬编码敏感信息
- 定期备份重要文件
- 使用安全的文件路径
- 避免执行不信任的命令

## 高级功能

### 文件内容替换
支持复杂的文本替换操作：

```python
# 示例：替换配置文件中的值
old_content = 'DEBUG = False'
new_content = 'DEBUG = True'
# 使用文件管理 -> 替换内容功能
```

### 正则表达式替换
```python
# 示例：替换所有的IP地址
pattern = r'\d+\.\d+\.\d+\.\d+'
replacement = '*************'
# 启用正则表达式选项
```

### 批量操作
虽然界面是交互式的，但您可以：
1. 编写脚本文件
2. 使用代码执行功能运行脚本
3. 实现批量文件操作

## 故障排除

### 常见问题

#### Pod 无法访问
- 检查 Pod 状态是否为 Running
- 验证端口是否正确映射
- 确认网络连接正常

#### 文件操作失败
- 检查文件路径是否正确
- 验证文件权限
- 确认磁盘空间充足

#### 代码执行错误
- 检查代码语法
- 验证依赖包是否安装
- 查看错误输出信息

#### AI功能不可用
- 检查网络连接
- 验证API密钥配置
- 确认AI服务状态

### 获取帮助
- 查看错误信息和日志
- 使用AI助手询问技术问题
- 参考项目文档和示例

## 技巧和窍门

### 快速导航
- 使用相对路径简化文件操作
- 善用文件搜索功能定位文件
- 利用目录结构组织项目

### 效率提升
- 使用AI助手生成样板代码
- 利用代码分析功能优化代码
- 使用文件替换功能批量修改

### 调试技巧
- 使用系统信息检查环境
- 利用命令执行功能调试问题
- 查看详细的错误输出信息
