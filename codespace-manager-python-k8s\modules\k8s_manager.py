import json
import requests
import yaml
import time
from pathlib import Path
from datetime import datetime


class K8sManager:
    def __init__(self, config=None):
        if config is None:
            config = self.load_config()
        
        self.api_server = config['kubernetes']['apiServer']
        self.namespace = config['kubernetes']['namespace']
        self.default_image = config['kubernetes']['defaultImage']
        self.port_range = config['kubernetes']['portRange']
        self.resources = config['kubernetes']['resources']
        self.volumes = config['kubernetes']['volumes']
        
        self.pods_file = Path.cwd() / 'data' / 'pods.json'
        self.ensure_data_directory()

    def load_config(self):
        """加载配置文件"""
        config_file = Path.cwd() / 'config.json'
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def ensure_data_directory(self):
        """确保数据目录存在"""
        data_dir = self.pods_file.parent
        data_dir.mkdir(exist_ok=True)
        
        if not self.pods_file.exists():
            with open(self.pods_file, 'w', encoding='utf-8') as f:
                json.dump({}, f)

    def load_pods(self):
        """加载Pod数据"""
        try:
            with open(self.pods_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as error:
            print(f'Error loading pods: {error}')
            return {}

    def save_pods(self, pods):
        """保存Pod数据"""
        try:
            with open(self.pods_file, 'w', encoding='utf-8') as f:
                json.dump(pods, f, indent=2, ensure_ascii=False)
        except Exception as error:
            print(f'Error saving pods: {error}')
            raise error

    def create_namespace(self, namespace_name):
        """创建命名空间"""
        try:
            namespace_spec = {
                "apiVersion": "v1",
                "kind": "Namespace",
                "metadata": {
                    "name": namespace_name,
                    "labels": {
                        "app": "codespace-manager",
                        "created-by": "codespace-manager-k8s"
                    }
                }
            }
            
            url = f"{self.api_server}/namespaces"
            headers = {'Content-Type': 'application/json'}
            
            response = requests.post(url, json=namespace_spec, headers=headers)
            
            if response.status_code in [200, 201, 409]:  # 409 表示已存在
                return True
            else:
                print(f"创建命名空间失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as error:
            print(f"创建命名空间异常: {error}")
            return False

    def generate_port_from_user_id(self, user_id):
        """基于用户ID生成端口号"""
        hash_value = 0
        for char in user_id:
            hash_value = ((hash_value << 5) - hash_value) + ord(char)
            hash_value = hash_value & 0xFFFFFFFF
        
        port_range = self.port_range['max'] - self.port_range['min']
        return abs(hash_value % port_range) + self.port_range['min']

    def create_user_pod(self, user_id, username, namespace=None):
        """为用户创建Pod"""
        try:
            pods = self.load_pods()
            
            # 检查用户是否已有Pod
            if user_id in pods:
                existing_pod = pods[user_id]
                if self.check_pod_status(existing_pod['podName'], existing_pod['namespace']):
                    print(f'用户 {username} 的Pod已存在: {existing_pod["podName"]}')
                    return existing_pod
                else:
                    print(f'用户 {username} 的旧Pod不存在，创建新Pod')
            
            # 使用用户的命名空间或默认命名空间
            pod_namespace = namespace or f"user-{user_id[:8]}"
            
            # 确保命名空间存在
            self.create_namespace(pod_namespace)
            
            # 生成Pod名称和端口
            pod_name = f"codespace-{user_id[:8]}"
            port = self.generate_port_from_user_id(user_id)
            
            # 创建Pod规格
            pod_spec = self.generate_pod_spec(pod_name, port, user_id)
            
            # 创建Pod
            url = f"{self.api_server}/namespaces/{pod_namespace}/pods"
            headers = {'Content-Type': 'application/json'}
            
            response = requests.post(url, json=pod_spec, headers=headers)
            
            if response.status_code in [200, 201]:
                # 创建Service暴露Pod
                service_created = self.create_service(pod_name, port, pod_namespace)
                
                pod_info = {
                    'podName': pod_name,
                    'podId': f"{pod_namespace}/{pod_name}",
                    'userId': user_id,
                    'username': username,
                    'namespace': pod_namespace,
                    'port': port,
                    'image': self.default_image,
                    'createdAt': datetime.now().isoformat(),
                    'status': 'creating',
                    'serviceCreated': service_created
                }
                
                # 保存Pod信息
                pods[user_id] = pod_info
                self.save_pods(pods)
                
                # 等待Pod启动
                self.wait_for_pod_ready(pod_name, pod_namespace)
                
                print(f'Pod创建成功: {pod_name}')
                print(f'访问地址: http://192.168.1.218:{port}')
                
                return pod_info
            else:
                raise Exception(f"创建Pod失败: {response.status_code} - {response.text}")
                
        except Exception as error:
            print(f'创建Pod失败: {error}')
            raise error

    def generate_pod_spec(self, pod_name, port, user_id):
        """生成Pod规格"""
        return {
            "apiVersion": "v1",
            "kind": "Pod",
            "metadata": {
                "name": pod_name,
                "labels": {
                    "app": "codespace",
                    "user": user_id[:8],
                    "created-by": "codespace-manager-k8s"
                }
            },
            "spec": {
                "containers": [
                    {
                        "name": "workspace",
                        "image": self.default_image,
                        "ports": [
                            {
                                "containerPort": 8080,
                                "name": "http"
                            }
                        ],
                        "env": [
                            {
                                "name": "USER_ID",
                                "value": user_id
                            },
                            {
                                "name": "WORKSPACE_DIR",
                                "value": self.volumes['workspace']
                            }
                        ],
                        "volumeMounts": [
                            {
                                "name": "workspace-volume",
                                "mountPath": self.volumes['workspace']
                            },
                            {
                                "name": "tmp-volume",
                                "mountPath": self.volumes['tmp']
                            }
                        ],
                        "resources": self.resources,
                        "command": ["/bin/bash"],
                        "args": ["-c", "mkdir -p /home/<USER>/home/<USER>"],
                        "workingDir": self.volumes['workspace']
                    }
                ],
                "volumes": [
                    {
                        "name": "workspace-volume",
                        "emptyDir": {}
                    },
                    {
                        "name": "tmp-volume",
                        "emptyDir": {}
                    }
                ],
                "restartPolicy": "Always"
            }
        }

    def create_service(self, pod_name, port, namespace):
        """为Pod创建Service"""
        try:
            service_spec = {
                "apiVersion": "v1",
                "kind": "Service",
                "metadata": {
                    "name": f"{pod_name}-service",
                    "namespace": namespace,
                    "labels": {
                        "app": "codespace",
                        "pod": pod_name
                    }
                },
                "spec": {
                    "type": "NodePort",
                    "ports": [
                        {
                            "port": 8080,
                            "targetPort": 8080,
                            "nodePort": port,
                            "protocol": "TCP"
                        }
                    ],
                    "selector": {
                        "app": "codespace",
                        "user": pod_name.split('-')[1]  # 从pod名称提取用户ID
                    }
                }
            }

            url = f"{self.api_server}/namespaces/{namespace}/services"
            headers = {'Content-Type': 'application/json'}

            response = requests.post(url, json=service_spec, headers=headers)

            if response.status_code in [200, 201]:
                return True
            else:
                print(f"创建Service失败: {response.status_code} - {response.text}")
                return False

        except Exception as error:
            print(f"创建Service异常: {error}")
            return False

    def wait_for_pod_ready(self, pod_name, namespace, timeout=300):
        """等待Pod就绪"""
        try:
            start_time = time.time()

            while time.time() - start_time < timeout:
                status = self.get_pod_status(pod_name, namespace)

                if status and status.get('phase') == 'Running':
                    # 检查容器是否就绪
                    container_statuses = status.get('containerStatuses', [])
                    if container_statuses and all(c.get('ready', False) for c in container_statuses):
                        print(f"Pod {pod_name} 已就绪")
                        return True

                time.sleep(5)

            print(f"Pod {pod_name} 启动超时")
            return False

        except Exception as error:
            print(f"等待Pod就绪异常: {error}")
            return False

    def get_pod_status(self, pod_name, namespace):
        """获取Pod状态"""
        try:
            url = f"{self.api_server}/namespaces/{namespace}/pods/{pod_name}"
            response = requests.get(url)

            if response.status_code == 200:
                pod_data = response.json()
                return pod_data.get('status', {})
            else:
                return None

        except Exception as error:
            print(f"获取Pod状态异常: {error}")
            return None

    def check_pod_status(self, pod_name, namespace):
        """检查Pod是否存在且运行"""
        try:
            status = self.get_pod_status(pod_name, namespace)
            return status and status.get('phase') == 'Running'
        except Exception as error:
            print(f"检查Pod状态异常: {error}")
            return False

    def delete_pod(self, pod_name, namespace):
        """删除Pod"""
        try:
            # 删除Service
            service_url = f"{self.api_server}/namespaces/{namespace}/services/{pod_name}-service"
            requests.delete(service_url)

            # 删除Pod
            pod_url = f"{self.api_server}/namespaces/{namespace}/pods/{pod_name}"
            response = requests.delete(pod_url)

            if response.status_code in [200, 202, 404]:  # 404表示已不存在
                return True
            else:
                print(f"删除Pod失败: {response.status_code} - {response.text}")
                return False

        except Exception as error:
            print(f"删除Pod异常: {error}")
            return False

    def get_user_pod(self, user_id):
        """获取用户的Pod信息"""
        try:
            pods = self.load_pods()
            return pods.get(user_id)
        except Exception as error:
            print(f"获取用户Pod异常: {error}")
            return None

    def list_pods(self):
        """列出所有Pod"""
        try:
            pods = self.load_pods()
            pod_list = []

            for user_id, pod_info in pods.items():
                # 获取实时状态
                status = self.get_pod_status(pod_info['podName'], pod_info['namespace'])
                pod_info['currentStatus'] = status.get('phase', 'Unknown') if status else 'NotFound'
                pod_list.append(pod_info)

            return pod_list
        except Exception as error:
            print(f"列出Pod异常: {error}")
            return []

    def execute_command_in_pod(self, pod_name, namespace, command, container_name="workspace"):
        """在Pod中执行命令"""
        try:
            # 构建exec URL
            exec_url = f"{self.api_server}/namespaces/{namespace}/pods/{pod_name}/exec"

            # 构建exec参数
            params = {
                'container': container_name,
                'command': command if isinstance(command, list) else ['/bin/bash', '-c', command],
                'stdin': 'false',
                'stdout': 'true',
                'stderr': 'true',
                'tty': 'false'
            }

            # 发送exec请求
            response = requests.post(exec_url, params=params)

            if response.status_code == 200:
                return {
                    'success': True,
                    'output': response.text,
                    'exitCode': 0
                }
            else:
                return {
                    'success': False,
                    'error': f"执行命令失败: {response.status_code} - {response.text}",
                    'exitCode': response.status_code
                }

        except Exception as error:
            return {
                'success': False,
                'error': f"执行命令异常: {error}",
                'exitCode': -1
            }
