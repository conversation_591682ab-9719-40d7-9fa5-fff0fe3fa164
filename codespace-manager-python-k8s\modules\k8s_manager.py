import json
import yaml
import time
import base64
from pathlib import Path
from datetime import datetime
from kubernetes import client, config, stream
from kubernetes.client.rest import ApiException


class K8sManager:
    def __init__(self, config_data=None):
        if config_data is None:
            config_data = self.load_config()

        # 加载 Kubernetes 配置
        try:
            # 尝试加载集群内配置
            config.load_incluster_config()
        except:
            # 如果不在集群内，加载本地配置
            try:
                config.load_kube_config()
            except:
                # 如果没有配置文件，使用自定义配置
                configuration = client.Configuration()
                configuration.host = config_data['kubernetes']['apiServer']
                client.Configuration.set_default(configuration)

        # 初始化 Kubernetes 客户端
        self.v1 = client.CoreV1Api()
        self.apps_v1 = client.AppsV1Api()

        self.namespace = config_data['kubernetes']['namespace']
        self.default_image = config_data['kubernetes']['defaultImage']
        self.port_range = config_data['kubernetes']['portRange']
        self.resources = config_data['kubernetes']['resources']
        self.volumes = config_data['kubernetes']['volumes']

        self.pods_file = Path.cwd() / 'data' / 'pods.json'
        self.ensure_data_directory()

    def load_config(self):
        """加载配置文件"""
        config_file = Path.cwd() / 'config.json'
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def ensure_data_directory(self):
        """确保数据目录存在"""
        data_dir = self.pods_file.parent
        data_dir.mkdir(exist_ok=True)
        
        if not self.pods_file.exists():
            with open(self.pods_file, 'w', encoding='utf-8') as f:
                json.dump({}, f)

    def load_pods(self):
        """加载Pod数据"""
        try:
            with open(self.pods_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as error:
            print(f'Error loading pods: {error}')
            return {}

    def save_pods(self, pods):
        """保存Pod数据"""
        try:
            with open(self.pods_file, 'w', encoding='utf-8') as f:
                json.dump(pods, f, indent=2, ensure_ascii=False)
        except Exception as error:
            print(f'Error saving pods: {error}')
            raise error

    def create_namespace(self, namespace_name):
        """创建命名空间"""
        try:
            # 检查命名空间是否已存在
            try:
                self.v1.read_namespace(name=namespace_name)
                print(f"命名空间 {namespace_name} 已存在")
                return True
            except ApiException as e:
                if e.status != 404:
                    raise e

            # 创建命名空间
            namespace = client.V1Namespace(
                metadata=client.V1ObjectMeta(
                    name=namespace_name,
                    labels={
                        "app": "codespace-manager",
                        "created-by": "codespace-manager-k8s"
                    }
                )
            )

            self.v1.create_namespace(body=namespace)
            print(f"命名空间 {namespace_name} 创建成功")
            return True

        except Exception as error:
            print(f"创建命名空间异常: {error}")
            return False

    def generate_port_from_user_id(self, user_id):
        """基于用户ID生成端口号"""
        hash_value = 0
        for char in user_id:
            hash_value = ((hash_value << 5) - hash_value) + ord(char)
            hash_value = hash_value & 0xFFFFFFFF
        
        port_range = self.port_range['max'] - self.port_range['min']
        return abs(hash_value % port_range) + self.port_range['min']

    def create_user_pod(self, user_id, username, namespace=None):
        """为用户创建Pod"""
        try:
            pods = self.load_pods()
            
            # 检查用户是否已有Pod
            if user_id in pods:
                existing_pod = pods[user_id]
                if self.check_pod_status(existing_pod['podName'], existing_pod['namespace']):
                    print(f'用户 {username} 的Pod已存在: {existing_pod["podName"]}')
                    return existing_pod
                else:
                    print(f'用户 {username} 的旧Pod不存在，创建新Pod')
            
            # 使用用户的命名空间或默认命名空间
            pod_namespace = namespace or f"user-{user_id[:8]}"
            
            # 确保命名空间存在
            self.create_namespace(pod_namespace)
            
            # 生成Pod名称和端口
            pod_name = f"codespace-{user_id[:8]}"
            port = self.generate_port_from_user_id(user_id)
            
            # 创建Pod
            pod_body = self.generate_pod_body(pod_name, port, user_id)

            try:
                pod_response = self.v1.create_namespaced_pod(
                    namespace=pod_namespace,
                    body=pod_body
                )

                # 创建Service暴露Pod
                service_created = self.create_service(pod_name, port, pod_namespace)

                pod_info = {
                    'podName': pod_name,
                    'podId': f"{pod_namespace}/{pod_name}",
                    'userId': user_id,
                    'username': username,
                    'namespace': pod_namespace,
                    'port': port,
                    'image': self.default_image,
                    'createdAt': datetime.now().isoformat(),
                    'status': 'creating',
                    'serviceCreated': service_created
                }

                # 保存Pod信息
                pods[user_id] = pod_info
                self.save_pods(pods)

                # 等待Pod启动
                self.wait_for_pod_ready(pod_name, pod_namespace)

                print(f'Pod创建成功: {pod_name}')
                print(f'访问地址: http://192.168.1.218:{port}')

                return pod_info
            except ApiException as e:
                raise Exception(f"创建Pod失败: {e.status} - {e.reason}")
                
        except Exception as error:
            print(f'创建Pod失败: {error}')
            raise error

    def generate_pod_body(self, pod_name, port, user_id):
        """生成Pod对象"""
        # 环境变量
        env_vars = [
            client.V1EnvVar(name="USER_ID", value=user_id),
            client.V1EnvVar(name="WORKSPACE_DIR", value=self.volumes['workspace']),
            client.V1EnvVar(name="PYTHONUNBUFFERED", value="1")
        ]

        # 卷挂载
        volume_mounts = [
            client.V1VolumeMount(
                name="workspace-volume",
                mount_path=self.volumes['workspace']
            ),
            client.V1VolumeMount(
                name="tmp-volume",
                mount_path=self.volumes['tmp']
            )
        ]

        # 端口
        ports = [
            client.V1ContainerPort(container_port=8080, name="http")
        ]

        # 资源限制
        resources = client.V1ResourceRequirements(
            requests=self.resources['requests'],
            limits=self.resources['limits']
        )

        # 启动命令
        startup_script = """
        # 创建工作目录
        mkdir -p /home/<USER>
        cd /home/<USER>

        # 安装基本工具
        apt-get update && apt-get install -y curl wget git vim nano tree htop python3-pip nodejs npm default-jdk gcc g++ make build-essential

        # 安装Python包
        pip3 install --upgrade pip
        pip3 install requests flask fastapi jupyter notebook

        # 创建示例文件
        cat > hello.py << 'EOF'
#!/usr/bin/env python3
print("Hello from CodeSpace Manager!")
print("This is your personal development environment.")
print("You can create, edit, and run code files here.")
EOF

        cat > hello.js << 'EOF'
console.log("Hello from Node.js!");
console.log("Welcome to your CodeSpace environment!");
EOF

        cat > README.md << 'EOF'
# Welcome to Your CodeSpace!

This is your personal development environment running in Kubernetes.

## Available Tools
- Python 3 with pip
- Node.js with npm
- Java Development Kit
- GCC/G++ for C/C++
- Git for version control
- Various text editors (vim, nano)

## Getting Started
1. Create your code files
2. Use the CodeSpace Manager to run them
3. Install additional packages as needed

Happy coding!
EOF

        # 设置权限
        chmod +x hello.py
        chmod 755 /home/<USER>

        # 启动HTTP服务器
        echo "Starting development environment..."
        python3 -m http.server 8080
        """

        # 容器定义
        container = client.V1Container(
            name="workspace",
            image=self.default_image,
            ports=ports,
            env=env_vars,
            volume_mounts=volume_mounts,
            resources=resources,
            command=["/bin/bash"],
            args=["-c", startup_script],
            working_dir=self.volumes['workspace']
        )

        # 卷定义
        volumes = [
            client.V1Volume(
                name="workspace-volume",
                empty_dir=client.V1EmptyDirVolumeSource()
            ),
            client.V1Volume(
                name="tmp-volume",
                empty_dir=client.V1EmptyDirVolumeSource()
            )
        ]

        # Pod规格
        pod_spec = client.V1PodSpec(
            containers=[container],
            volumes=volumes,
            restart_policy="Always"
        )

        # Pod对象
        pod = client.V1Pod(
            metadata=client.V1ObjectMeta(
                name=pod_name,
                labels={
                    "app": "codespace",
                    "user": user_id[:8],
                    "created-by": "codespace-manager-k8s"
                }
            ),
            spec=pod_spec
        )

        return pod

    def create_service(self, pod_name, port, namespace):
        """为Pod创建Service"""
        try:
            # 检查Service是否已存在
            service_name = f"{pod_name}-service"
            try:
                self.v1.read_namespaced_service(name=service_name, namespace=namespace)
                print(f"Service {service_name} 已存在")
                return True
            except ApiException as e:
                if e.status != 404:
                    raise e

            # 创建Service
            service_ports = [
                client.V1ServicePort(
                    port=8080,
                    target_port=8080,
                    node_port=port,
                    protocol="TCP",
                    name="http"
                )
            ]

            service_spec = client.V1ServiceSpec(
                type="NodePort",
                ports=service_ports,
                selector={
                    "app": "codespace",
                    "user": pod_name.split('-')[1]  # 从pod名称提取用户ID
                }
            )

            service = client.V1Service(
                metadata=client.V1ObjectMeta(
                    name=service_name,
                    labels={
                        "app": "codespace",
                        "pod": pod_name
                    }
                ),
                spec=service_spec
            )

            self.v1.create_namespaced_service(namespace=namespace, body=service)
            print(f"Service {service_name} 创建成功")
            return True

        except Exception as error:
            print(f"创建Service异常: {error}")
            return False

    def wait_for_pod_ready(self, pod_name, namespace, timeout=300):
        """等待Pod就绪"""
        try:
            start_time = time.time()

            while time.time() - start_time < timeout:
                status = self.get_pod_status(pod_name, namespace)

                if status and status.get('phase') == 'Running':
                    # 检查容器是否就绪
                    container_statuses = status.get('containerStatuses', [])
                    if container_statuses and all(c.get('ready', False) for c in container_statuses):
                        print(f"Pod {pod_name} 已就绪")
                        return True

                time.sleep(5)

            print(f"Pod {pod_name} 启动超时")
            return False

        except Exception as error:
            print(f"等待Pod就绪异常: {error}")
            return False

    def get_pod_status(self, pod_name, namespace):
        """获取Pod状态"""
        try:
            pod = self.v1.read_namespaced_pod(name=pod_name, namespace=namespace)
            return {
                'phase': pod.status.phase,
                'containerStatuses': [
                    {
                        'ready': status.ready,
                        'restartCount': status.restart_count,
                        'state': status.state
                    } for status in (pod.status.container_statuses or [])
                ]
            }
        except ApiException as e:
            if e.status == 404:
                return None
            print(f"获取Pod状态异常: {e}")
            return None
        except Exception as error:
            print(f"获取Pod状态异常: {error}")
            return None

    def check_pod_status(self, pod_name, namespace):
        """检查Pod是否存在且运行"""
        try:
            status = self.get_pod_status(pod_name, namespace)
            return status and status.get('phase') == 'Running'
        except Exception as error:
            print(f"检查Pod状态异常: {error}")
            return False

    def delete_pod(self, pod_name, namespace):
        """删除Pod"""
        try:
            # 删除Service
            try:
                self.v1.delete_namespaced_service(
                    name=f"{pod_name}-service",
                    namespace=namespace
                )
            except ApiException as e:
                if e.status != 404:
                    print(f"删除Service失败: {e}")

            # 删除Pod
            try:
                self.v1.delete_namespaced_pod(
                    name=pod_name,
                    namespace=namespace
                )
                return True
            except ApiException as e:
                if e.status == 404:
                    return True  # 已不存在
                print(f"删除Pod失败: {e}")
                return False

        except Exception as error:
            print(f"删除Pod异常: {error}")
            return False

    def get_user_pod(self, user_id):
        """获取用户的Pod信息"""
        try:
            pods = self.load_pods()
            return pods.get(user_id)
        except Exception as error:
            print(f"获取用户Pod异常: {error}")
            return None

    def list_pods(self):
        """列出所有Pod"""
        try:
            pods = self.load_pods()
            pod_list = []

            for user_id, pod_info in pods.items():
                # 获取实时状态
                status = self.get_pod_status(pod_info['podName'], pod_info['namespace'])
                pod_info['currentStatus'] = status.get('phase', 'Unknown') if status else 'NotFound'
                pod_list.append(pod_info)

            return pod_list
        except Exception as error:
            print(f"列出Pod异常: {error}")
            return []

    def execute_command_in_pod(self, pod_name, namespace, command, container_name="workspace"):
        """在Pod中执行命令"""
        try:
            # 准备命令
            if isinstance(command, str):
                exec_command = ['/bin/bash', '-c', command]
            else:
                exec_command = command

            # 执行命令
            resp = stream.stream(
                self.v1.connect_get_namespaced_pod_exec,
                pod_name,
                namespace,
                command=exec_command,
                container=container_name,
                stderr=True,
                stdin=False,
                stdout=True,
                tty=False,
                _preload_content=False
            )

            # 读取输出
            output = ""
            error_output = ""

            while resp.is_open():
                resp.update(timeout=1)
                if resp.peek_stdout():
                    output += resp.read_stdout()
                if resp.peek_stderr():
                    error_output += resp.read_stderr()
                if not resp.is_open():
                    break

            resp.close()

            return {
                'success': True,
                'output': output,
                'error': error_output,
                'exitCode': 0
            }

        except Exception as error:
            return {
                'success': False,
                'error': f"执行命令异常: {error}",
                'exitCode': -1
            }
