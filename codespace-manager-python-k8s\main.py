#!/usr/bin/env python3

import sys
import os
import tempfile
from pathlib import Path
from colorama import init, Fore, Style
from inquirer import prompt, List, Text, Password, Confirm, Editor

# 初始化colorama
init(autoreset=True)

# 添加模块路径
sys.path.append(str(Path(__file__).parent / 'modules'))

from user_manager import UserManager
from k8s_manager import K8sManager
from ai_manager import AIManager
from file_manager import FileManager
from code_executor import CodeExecutor

# 导入菜单功能
from main_menus import MenuMixin


class CodeSpaceManagerK8s(MenuMixin):
    def __init__(self):
        self.user_manager = UserManager()
        self.k8s_manager = K8sManager()
        self.ai_manager = AIManager()
        self.file_manager = FileManager(self.k8s_manager)
        self.code_executor = CodeExecutor(self.k8s_manager)
        self.current_user = None

    def start(self):
        """启动应用"""
        print(f"{Fore.BLUE}{Style.BRIGHT}\n🚀 欢迎使用 CodeSpace Manager - Kubernetes 版本")
        print(f"{Fore.LIGHTBLACK_EX}基于 Kubernetes 的智能容器管理系统\n")

        # 测试AI连接
        print(f"{Fore.YELLOW}正在测试AI连接...")
        ai_test = self.ai_manager.test_connection()
        if ai_test['success']:
            print(f"{Fore.GREEN}✅ AI连接成功")
        else:
            print(f"{Fore.RED}❌ AI连接失败: {ai_test['error']}")

        # 测试Kubernetes连接
        print(f"{Fore.YELLOW}正在测试Kubernetes连接...")
        try:
            # 尝试创建默认命名空间
            k8s_test = self.k8s_manager.create_namespace(self.k8s_manager.namespace)
            if k8s_test:
                print(f"{Fore.GREEN}✅ Kubernetes连接成功")
            else:
                print(f"{Fore.RED}❌ Kubernetes连接失败")
        except Exception as e:
            print(f"{Fore.RED}❌ Kubernetes连接异常: {e}")

        while True:
            try:
                if not self.current_user:
                    self.show_auth_menu()
                else:
                    self.show_main_menu()
            except KeyboardInterrupt:
                print(f"\n{Fore.BLUE}再见！")
                sys.exit(0)
            except Exception as error:
                print(f"{Fore.RED}发生错误: {error}")
                self.press_enter_to_continue()

    def show_auth_menu(self):
        """显示认证菜单"""
        questions = [
            List('action',
                 message='请选择操作:',
                 choices=[
                     ('🔐 登录', 'login'),
                     ('📝 注册', 'register'),
                     ('❌ 退出', 'exit')
                 ])
        ]
        
        answers = prompt(questions)
        if not answers:
            return
        
        action = answers['action']
        
        if action == 'login':
            self.handle_login()
        elif action == 'register':
            self.handle_register()
        elif action == 'exit':
            print(f"{Fore.BLUE}再见！")
            sys.exit(0)

    def handle_login(self):
        """处理登录"""
        questions = [
            Text('username', message='用户名:', validate=lambda _, x: x.strip() != '' or '用户名不能为空'),
            Password('password', message='密码:', validate=lambda _, x: x.strip() != '' or '密码不能为空')
        ]
        
        answers = prompt(questions)
        if not answers:
            return
        
        username = answers['username']
        password = answers['password']
        
        try:
            result = self.user_manager.login_user(username, password)
            if result['success']:
                self.current_user = result['user']
                print(f"{Fore.GREEN}✅ 登录成功！欢迎回来，{username}")
                
                # 检查或创建用户Pod
                self.ensure_user_pod()
        except Exception as error:
            print(f"{Fore.RED}❌ 登录失败: {error}")
            self.press_enter_to_continue()

    def handle_register(self):
        """处理注册"""
        questions = [
            Text('username', message='用户名:', validate=lambda _, x: x.strip() != '' or '用户名不能为空'),
            Password('password', message='密码:', validate=lambda _, x: len(x) >= 6 or '密码至少6位'),
            Password('confirm_password', message='确认密码:')
        ]
        
        answers = prompt(questions)
        if not answers:
            return
        
        username = answers['username']
        password = answers['password']
        confirm_password = answers['confirm_password']
        
        if password != confirm_password:
            print(f"{Fore.RED}❌ 密码不匹配")
            self.press_enter_to_continue()
            return
        
        try:
            result = self.user_manager.register_user(username, password)
            if result['success']:
                print(f"{Fore.GREEN}✅ 注册成功！")
                print(f"{Fore.CYAN}用户ID: {result['userId']}")
                print(f"{Fore.CYAN}命名空间: {result['namespace']}")
                print(f"{Fore.YELLOW}请使用新账户登录")
        except Exception as error:
            print(f"{Fore.RED}❌ 注册失败: {error}")
        
        self.press_enter_to_continue()

    def ensure_user_pod(self):
        """确保用户有可用Pod"""
        print(f"{Fore.YELLOW}正在检查Pod状态...")
        
        try:
            # 首先检查是否已有Pod记录
            existing_pod = self.k8s_manager.get_user_pod(self.current_user['id'])
            
            if existing_pod:
                print(f"{Fore.CYAN}📦 发现现有Pod: {existing_pod['podName']}")
                
                # 检查Pod是否还在运行
                pod_status = self.k8s_manager.check_pod_status(
                    existing_pod['podName'], 
                    existing_pod['namespace']
                )
                
                if pod_status:
                    print(f"{Fore.GREEN}✅ Pod已在运行")
                    print(f"{Fore.BLUE}🌐 访问地址: http://192.168.1.218:{existing_pod['port']}")
                    self.current_user['podId'] = existing_pod['podId']
                    return
                else:
                    print(f"{Fore.YELLOW}⚠️ Pod不存在或未运行，将创建新Pod")
            else:
                print(f"{Fore.CYAN}🆕 首次登录，创建新Pod...")
            
            # 创建新Pod
            pod_info = self.k8s_manager.create_user_pod(
                self.current_user['id'],
                self.current_user['username'],
                self.current_user.get('namespace')
            )
            
            if pod_info:
                print(f"{Fore.GREEN}✅ Pod创建成功: {pod_info['podName']}")
                print(f"{Fore.BLUE}🌐 访问地址: http://192.168.1.218:{pod_info['port']}")
                
                # 更新用户的Pod ID
                self.user_manager.update_user_pod(self.current_user['username'], pod_info['podId'])
                self.current_user['podId'] = pod_info['podId']
        except Exception as error:
            print(f"{Fore.RED}❌ Pod操作失败: {error}")

    def show_main_menu(self):
        """显示主菜单"""
        print(f"\n{Fore.CYAN}👤 当前用户: {self.current_user['username']}")
        print(f"{Fore.LIGHTBLACK_EX}🆔 用户ID: {self.current_user['id']}")
        print(f"{Fore.LIGHTBLACK_EX}🏷️ 命名空间: {self.current_user.get('namespace', 'default')}")
        
        questions = [
            List('action',
                 message='请选择操作:',
                 choices=[
                     ('🤖 AI助手', 'ai'),
                     ('📁 文件管理', 'files'),
                     ('⚡ 代码执行', 'execute'),
                     ('🔧 系统信息', 'system'),
                     ('📊 Pod管理', 'pods'),
                     ('🚪 退出登录', 'logout')
                 ])
        ]
        
        answers = prompt(questions)
        if not answers:
            return
        
        action = answers['action']
        
        if action == 'ai':
            self.show_ai_menu()
        elif action == 'files':
            self.show_file_menu()
        elif action == 'execute':
            self.show_execute_menu()
        elif action == 'system':
            self.show_system_info()
        elif action == 'pods':
            self.show_pod_menu()
        elif action == 'logout':
            self.current_user = None
            print(f"{Fore.YELLOW}已退出登录")

    def show_ai_menu(self):
        """显示AI菜单"""
        questions = [
            List('action',
                 message='选择AI功能:',
                 choices=[
                     ('💬 自由对话', 'chat'),
                     ('📝 生成代码', 'generate'),
                     ('🔍 分析代码', 'analyze'),
                     ('🔙 返回主菜单', 'back')
                 ])
        ]

        answers = prompt(questions)
        if not answers:
            return

        action = answers['action']

        if action == 'chat':
            self.handle_ai_chat()
        elif action == 'generate':
            self.handle_code_generation()
        elif action == 'analyze':
            self.handle_code_analysis()
        elif action == 'back':
            return

    def handle_ai_chat(self):
        """处理AI对话"""
        questions = [
            Text('prompt', message='请输入您的问题:', validate=lambda _, x: x.strip() != '' or '问题不能为空')
        ]

        answers = prompt(questions)
        if not answers:
            return

        prompt_text = answers['prompt']

        print(f"{Fore.YELLOW}AI正在思考...")
        result = self.ai_manager.generate_response(prompt_text)

        if result['success']:
            print(f"{Fore.GREEN}\n🤖 AI回复:")
            print(f"{Fore.WHITE}{result['response']}")
        else:
            print(f"{Fore.RED}❌ AI请求失败: {result['error']}")

        self.press_enter_to_continue()

    def handle_code_generation(self):
        """处理代码生成"""
        questions = [
            Text('description', message='请描述您想要的代码功能:', validate=lambda _, x: x.strip() != '' or '描述不能为空'),
            List('language', message='选择编程语言:', choices=['python', 'javascript', 'java', 'cpp', 'c', 'bash'])
        ]

        answers = prompt(questions)
        if not answers:
            return

        description = answers['description']
        language = answers['language']

        print(f"{Fore.YELLOW}AI正在生成代码...")
        result = self.ai_manager.generate_code_from_description(description, language)

        if result['success']:
            print(f"{Fore.GREEN}\n💻 生成的代码:")
            print(f"{Fore.WHITE}{result['response']}")

            save_question = [
                Confirm('save_code', message='是否保存代码到Pod中?', default=False)
            ]
            save_answers = prompt(save_question)

            if save_answers and save_answers['save_code']:
                self.save_generated_code(result['response'], language)
        else:
            print(f"{Fore.RED}❌ 代码生成失败: {result['error']}")

        self.press_enter_to_continue()

    def handle_code_analysis(self):
        """处理代码分析"""
        # 首先列出可分析的文件
        file_list_result = self.file_manager.list_files(self.current_user['id'], '/home/<USER>')

        if not file_list_result['success']:
            print(f"{Fore.RED}❌ 获取文件列表失败: {file_list_result['error']}")
            self.press_enter_to_continue()
            return

        code_files = [
            file for file in file_list_result['files']
            if not file['isDirectory'] and any(file['name'].endswith(ext) for ext in ['.py', '.js', '.java', '.cpp', '.c', '.sh'])
        ]

        if len(code_files) == 0:
            print(f"{Fore.YELLOW}📁 当前目录没有可分析的代码文件")
            self.press_enter_to_continue()
            return

        choices = [(f"{self.get_file_icon(file['name'])} {file['name']}", file['name']) for file in code_files]
        choices.append(('🔙 返回', 'back'))

        questions = [
            List('choice', message='选择要分析的文件:', choices=choices)
        ]

        answers = prompt(questions)
        if not answers or answers['choice'] == 'back':
            return

        file_name = answers['choice']
        file_path = f'/home/<USER>/{file_name}'

        # 读取文件内容
        read_result = self.file_manager.read_file(self.current_user['id'], file_path)
        if not read_result['success']:
            print(f"{Fore.RED}❌ 读取文件失败: {read_result['error']}")
            self.press_enter_to_continue()
            return

        # 检测语言
        language = self.code_executor.detect_language(file_name) or 'text'

        print(f"{Fore.YELLOW}AI正在分析代码...")
        result = self.ai_manager.analyze_code(read_result['content'], language)

        if result['success']:
            print(f"{Fore.GREEN}\n🔍 代码分析结果:")
            print(f"{Fore.WHITE}{result['response']}")
        else:
            print(f"{Fore.RED}❌ 代码分析失败: {result['error']}")

        self.press_enter_to_continue()

    def save_generated_code(self, code, language):
        """保存生成的代码"""
        extensions = {
            'python': 'py',
            'javascript': 'js',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'bash': 'sh'
        }

        import time
        default_filename = f'generated_{int(time.time())}'

        questions = [
            Text('filename', message='文件名 (不含扩展名):', default=default_filename,
                 validate=lambda _, x: x.strip() != '' or '文件名不能为空')
        ]

        answers = prompt(questions)
        if not answers:
            return

        filename = answers['filename']
        full_filename = f"{filename}.{extensions[language]}"
        result = self.file_manager.write_file(self.current_user['id'], f'/home/<USER>/{full_filename}', code)

        if result['success']:
            print(f"{Fore.GREEN}✅ 代码已保存为: {full_filename}")
        else:
            print(f"{Fore.RED}❌ 保存失败: {result['error']}")

    def get_file_icon(self, filename):
        """获取文件图标"""
        ext = filename.split('.')[-1].lower()
        icons = {
            'py': '🐍',
            'js': '🟨',
            'java': '☕',
            'cpp': '⚙️',
            'c': '🔧',
            'sh': '📜'
        }
        return icons.get(ext, '📄')

    def show_execute_menu(self):
        """显示执行菜单"""
        questions = [
            List('action',
                 message='选择执行操作:',
                 choices=[
                     ('⚡ 执行命令', 'command'),
                     ('📄 运行文件', 'file'),
                     ('📦 安装包', 'install'),
                     ('🔙 返回主菜单', 'back')
                 ])
        ]

        answers = prompt(questions)
        if not answers:
            return

        action = answers['action']

        if action == 'command':
            self.execute_command()
        elif action == 'file':
            self.run_file()
        elif action == 'install':
            self.install_package()
        elif action == 'back':
            return

    def execute_command(self):
        """执行命令"""
        questions = [
            Text('command', message='要执行的命令:', validate=lambda _, x: x.strip() != '' or '命令不能为空'),
            Text('working_dir', message='工作目录:', default='/home/<USER>')
        ]

        answers = prompt(questions)
        if not answers:
            return

        command = answers['command']
        working_dir = answers['working_dir']

        print(f"{Fore.YELLOW}正在执行命令...")
        result = self.code_executor.execute_command(self.current_user['id'], command, working_dir)

        self.display_execution_result(result)
        self.press_enter_to_continue()

    def run_file(self):
        """运行文件"""
        # 首先列出当前目录的文件
        print(f"{Fore.YELLOW}正在获取文件列表...")
        file_list_result = self.file_manager.list_files(self.current_user['id'], '/home/<USER>')

        if not file_list_result['success']:
            print(f"{Fore.RED}❌ 获取文件列表失败: {file_list_result['error']}")
            self.press_enter_to_continue()
            return

        executable_files = [
            file for file in file_list_result['files']
            if not file['isDirectory'] and any(file['name'].endswith(ext) for ext in ['.py', '.js', '.java', '.cpp', '.c', '.sh'])
        ]

        if len(executable_files) == 0:
            print(f"{Fore.YELLOW}📁 当前目录没有可执行的代码文件")
            print(f"{Fore.LIGHTBLACK_EX}支持的文件类型: .py, .js, .java, .cpp, .c, .sh")
            self.press_enter_to_continue()
            return

        choices = [(f"{self.get_file_icon(file['name'])} {file['name']}", file['name']) for file in executable_files]
        choices.append(('🔙 返回', 'back'))

        questions = [
            List('choice', message='选择要运行的文件:', choices=choices)
        ]

        answers = prompt(questions)
        if not answers or answers['choice'] == 'back':
            return

        choice = answers['choice']

        args_questions = [
            Text('args', message='命令行参数 (可选):', default='')
        ]

        args_answers = prompt(args_questions)
        if not args_answers:
            return

        args = args_answers['args']

        print(f"{Fore.YELLOW}正在运行文件: {choice}")
        result = self.code_executor.run_file(self.current_user['id'], f'/home/<USER>/{choice}', args)

        self.display_execution_result(result)
        self.press_enter_to_continue()

    def install_package(self):
        """安装包"""
        questions = [
            List('package_manager', message='选择包管理器:',
                 choices=[
                     ('🐍 pip (Python)', 'pip'),
                     ('📦 npm (Node.js)', 'npm'),
                     ('🔧 apt (系统包)', 'apt')
                 ]),
            Text('package_name', message='包名:', validate=lambda _, x: x.strip() != '' or '包名不能为空')
        ]

        answers = prompt(questions)
        if not answers:
            return

        package_manager = answers['package_manager']
        package_name = answers['package_name']

        print(f"{Fore.YELLOW}正在安装 {package_name}...")
        result = self.code_executor.install_package(self.current_user['id'], package_manager, package_name)

        self.display_execution_result(result)
        self.press_enter_to_continue()

    def show_system_info(self):
        """显示系统信息"""
        print(f"{Fore.YELLOW}正在获取系统信息...")
        result = self.code_executor.get_system_info(self.current_user['id'])

        if result['success']:
            print(f"{Fore.GREEN}\n🔧 系统信息:")
            for key, info in result['systemInfo'].items():
                status = '✅' if info['available'] else '❌'
                print(f"{Fore.CYAN}{status} {key.upper()}:")
                print(f"{Fore.WHITE}   {info['output']}")
        else:
            print(f"{Fore.RED}❌ 获取系统信息失败: {result['error']}")

        self.press_enter_to_continue()

    def show_pod_menu(self):
        """显示Pod管理菜单"""
        questions = [
            List('action',
                 message='选择Pod操作:',
                 choices=[
                     ('📊 Pod状态', 'status'),
                     ('📋 列出所有Pod', 'list'),
                     ('🔄 重启Pod', 'restart'),
                     ('🗑️ 删除Pod', 'delete'),
                     ('🔙 返回主菜单', 'back')
                 ])
        ]

        answers = prompt(questions)
        if not answers:
            return

        action = answers['action']

        if action == 'status':
            self.show_pod_status()
        elif action == 'list':
            self.list_all_pods()
        elif action == 'restart':
            self.restart_pod()
        elif action == 'delete':
            self.delete_pod()
        elif action == 'back':
            return

    def show_pod_status(self):
        """显示当前用户Pod状态"""
        pod_info = self.k8s_manager.get_user_pod(self.current_user['id'])

        if not pod_info:
            print(f"{Fore.RED}❌ 用户Pod不存在")
            self.press_enter_to_continue()
            return

        print(f"{Fore.YELLOW}正在获取Pod状态...")
        status = self.k8s_manager.get_pod_status(pod_info['podName'], pod_info['namespace'])

        print(f"{Fore.GREEN}\n📊 Pod状态:")
        print(f"{Fore.CYAN}Pod名称: {pod_info['podName']}")
        print(f"{Fore.CYAN}命名空间: {pod_info['namespace']}")
        print(f"{Fore.CYAN}端口: {pod_info['port']}")
        print(f"{Fore.CYAN}镜像: {pod_info['image']}")
        print(f"{Fore.CYAN}创建时间: {pod_info['createdAt']}")

        if status:
            print(f"{Fore.CYAN}当前状态: {status.get('phase', 'Unknown')}")
            print(f"{Fore.BLUE}🌐 访问地址: http://192.168.1.218:{pod_info['port']}")
        else:
            print(f"{Fore.RED}状态: Pod不存在或无法访问")

        self.press_enter_to_continue()

    def list_all_pods(self):
        """列出所有Pod"""
        print(f"{Fore.YELLOW}正在获取Pod列表...")
        pods = self.k8s_manager.list_pods()

        if pods:
            print(f"{Fore.GREEN}\n📋 所有Pod列表:")
            for pod in pods:
                status_color = Fore.GREEN if pod.get('currentStatus') == 'Running' else Fore.RED
                print(f"{Fore.CYAN}Pod: {pod['podName']}")
                print(f"  用户: {pod['username']}")
                print(f"  命名空间: {pod['namespace']}")
                print(f"  端口: {pod['port']}")
                print(f"  {status_color}状态: {pod.get('currentStatus', 'Unknown')}")
                print()
        else:
            print(f"{Fore.YELLOW}没有找到任何Pod")

        self.press_enter_to_continue()

    def display_execution_result(self, result):
        """显示执行结果"""
        print(f"{Fore.CYAN}\n📊 执行结果:")
        print(f"{Fore.LIGHTBLACK_EX}命令: {result['command']}")
        print(f"{Fore.LIGHTBLACK_EX}工作目录: {result['workingDir']}")
        print(f"{Fore.LIGHTBLACK_EX}退出码: {result['exitCode']}")
        print(f"{Fore.LIGHTBLACK_EX}状态: {'✅ 成功' if result['success'] else '❌ 失败'}")

        if 'executionTime' in result:
            print(f"{Fore.LIGHTBLACK_EX}执行时间: {result['executionTime']:.2f}秒")

        if result['stdout']:
            print(f"{Fore.GREEN}\n📤 标准输出:")
            print(result['stdout'])

        if result['stderr']:
            print(f"{Fore.RED}\n📥 错误输出:")
            print(result['stderr'])

    def press_enter_to_continue(self):
        """按回车继续"""
        input("按回车键继续...")


if __name__ == "__main__":
    app = CodeSpaceManagerK8s()
    try:
        app.start()
    except Exception as error:
        print(f"{Fore.RED}应用启动失败: {error}")
        sys.exit(1)
