# CodeSpace Manager - Kubernetes 版本

基于 Kubernetes 的智能容器管理系统，为每个用户提供独立的 Pod 开发环境。

## 项目概述

本项目是 `codespace-manager-python` 的 Kubernetes 版本，主要改进包括：

1. **Kubernetes 集成**: 使用 Kubernetes API 管理容器，而不是直接使用 Docker
2. **优化文件操作**: 使用更规范的文件写入方式，支持大文件和特殊字符
3. **文件内容替换**: 支持对工作空间文件进行精确的内容替换和修改
4. **云原生架构**: 工作空间直接存储在 K8s 容器中，不依赖本地存储

## 核心功能

### 🎯 三大核心功能

1. **用户 Pod 自动分配功能**
   - 为每个用户自动创建专属的 Kubernetes Pod
   - 基于用户ID自动分配端口和命名空间
   - 智能资源管理和生命周期控制

2. **容器文件读写功能**
   - 完整的 Pod 内文件系统操作
   - 支持文件读取、写入、删除、搜索、替换
   - 规范的文件操作方式，无字符数量限制

3. **容器代码执行功能**
   - 多语言代码执行支持
   - 包管理和环境配置
   - Kubernetes 环境自动化管理

## 技术架构

### Kubernetes 集成
- **API Server**: http://*************:8001/api/v1
- **基础镜像**: python:bullseye
- **命名空间**: 每个用户独立的命名空间
- **服务暴露**: NodePort 或 LoadBalancer

### 文件操作优化
- 使用 Kubernetes API 的 exec 功能
- 支持流式文件传输
- 二进制安全的文件操作
- 支持文件内容的精确替换

## 安装和使用

### 环境要求
- Python 3.7+
- 可访问的 Kubernetes 集群
- kubectl 配置正确

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动应用
```bash
python run.py
```

## 项目结构

```
codespace-manager-python-k8s/
├── main.py                        # 主入口文件
├── run.py                         # 启动脚本
├── modules/                       # 功能模块目录
│   ├── __init__.py                # 模块初始化文件
│   ├── user_manager.py            # 用户管理模块
│   ├── k8s_manager.py             # Kubernetes 管理模块
│   ├── file_manager.py            # 文件管理模块
│   ├── code_executor.py           # 代码执行模块
│   └── ai_manager.py              # AI助手模块
├── data/                          # 数据存储目录
│   ├── users.json                 # 用户数据文件
│   └── pods.json                  # Pod 信息文件
├── k8s/                           # Kubernetes 配置文件
│   ├── pod-template.yaml          # Pod 模板
│   └── service-template.yaml      # Service 模板
├── docs/                          # 项目文档目录
└── requirements.txt               # Python 依赖
```

## 主要改进

### 1. Kubernetes 原生支持
- 使用 Kubernetes Python 客户端
- 支持 Pod、Service、ConfigMap 等资源管理
- 集成 Kubernetes 的网络和存储功能

### 2. 优化的文件操作
- 不再使用 echo 命令写入文件
- 支持二进制文件和大文件
- 提供文件内容替换功能
- 支持正则表达式匹配和替换

### 3. 增强的用户体验
- 实时的 Pod 状态监控
- 更好的错误处理和恢复
- 支持多种文件编辑模式

## 配置说明

### Kubernetes 配置
```json
{
  "kubernetes": {
    "apiServer": "http://*************:8001/api/v1",
    "namespace": "codespace",
    "image": "python:bullseye",
    "resources": {
      "requests": {
        "cpu": "100m",
        "memory": "256Mi"
      },
      "limits": {
        "cpu": "500m",
        "memory": "1Gi"
      }
    }
  }
}
```

## 开发指南

### 添加新功能
1. 在 `modules/` 下创建新模块
2. 在 `main.py` 中集成新功能
3. 更新配置文件和文档
4. 编写测试用例

### 代码规范
- 使用 Python 3.7+ 语法
- 遵循 PEP 8 代码规范
- 错误处理使用 try-except
- 添加适当的日志输出

## 许可证

MIT License
