import json
import requests
from pathlib import Path


class AIManager:
    def __init__(self):
        self.config = self.load_config()
        self.api_key = self.config['ai']['apiKey']
        self.base_url = self.config['ai']['baseUrl']
        self.model = self.config['ai']['model']
        self.provider = self.config['ai']['provider']

    def load_config(self):
        """加载配置文件"""
        config_file = Path.cwd() / 'config.json'
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def test_connection(self):
        """测试AI连接"""
        try:
            response = self.generate_response("Hello", max_tokens=10)
            return response
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }

    def generate_response(self, prompt, max_tokens=1000, temperature=0.7):
        """生成AI响应"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': self.model,
                'messages': [
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'max_tokens': max_tokens,
                'temperature': temperature
            }
            
            response = requests.post(
                f'{self.base_url}/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                return {
                    'success': True,
                    'response': content
                }
            else:
                return {
                    'success': False,
                    'error': f'API请求失败: {response.status_code} - {response.text}'
                }
                
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }

    def generate_code_from_description(self, description, language='python'):
        """根据描述生成代码"""
        try:
            prompt = f"""
请根据以下描述生成{language}代码：

描述：{description}

要求：
1. 代码应该完整且可运行
2. 包含必要的注释
3. 遵循{language}的最佳实践
4. 如果需要导入库，请包含import语句

请只返回代码，不要包含其他解释文字。
"""
            
            return self.generate_response(prompt, max_tokens=2000, temperature=0.3)
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }

    def analyze_code(self, code, language='python'):
        """分析代码"""
        try:
            prompt = f"""
请分析以下{language}代码：

```{language}
{code}
```

请提供：
1. 代码功能说明
2. 潜在的问题或改进建议
3. 代码质量评估
4. 性能优化建议（如果有）

请用中文回答。
"""
            
            return self.generate_response(prompt, max_tokens=1500, temperature=0.5)
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }

    def explain_error(self, error_message, code_context=''):
        """解释错误信息"""
        try:
            prompt = f"""
请解释以下错误信息并提供解决方案：

错误信息：
{error_message}

代码上下文：
{code_context}

请提供：
1. 错误原因分析
2. 具体的解决方案
3. 如何避免类似错误

请用中文回答。
"""
            
            return self.generate_response(prompt, max_tokens=1000, temperature=0.3)
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }

    def suggest_improvements(self, code, language='python'):
        """建议代码改进"""
        try:
            prompt = f"""
请为以下{language}代码提供改进建议：

```{language}
{code}
```

请从以下方面提供建议：
1. 代码结构和组织
2. 性能优化
3. 可读性改进
4. 错误处理
5. 最佳实践

请用中文回答，并提供具体的改进代码示例。
"""
            
            return self.generate_response(prompt, max_tokens=2000, temperature=0.4)
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }

    def generate_documentation(self, code, language='python'):
        """生成代码文档"""
        try:
            prompt = f"""
请为以下{language}代码生成详细的文档：

```{language}
{code}
```

请包含：
1. 功能概述
2. 参数说明
3. 返回值说明
4. 使用示例
5. 注意事项

请用中文编写文档。
"""
            
            return self.generate_response(prompt, max_tokens=1500, temperature=0.3)
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }

    def generate_test_cases(self, code, language='python'):
        """生成测试用例"""
        try:
            prompt = f"""
请为以下{language}代码生成测试用例：

```{language}
{code}
```

请生成：
1. 正常情况的测试用例
2. 边界条件的测试用例
3. 异常情况的测试用例
4. 使用适当的测试框架（如pytest for Python）

请只返回测试代码。
"""
            
            return self.generate_response(prompt, max_tokens=2000, temperature=0.3)
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }

    def translate_code(self, code, from_language, to_language):
        """翻译代码到另一种语言"""
        try:
            prompt = f"""
请将以下{from_language}代码翻译为{to_language}：

```{from_language}
{code}
```

要求：
1. 保持原有功能不变
2. 遵循目标语言的最佳实践
3. 包含必要的注释
4. 确保代码可以正常运行

请只返回翻译后的代码。
"""
            
            return self.generate_response(prompt, max_tokens=2000, temperature=0.3)
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }
