# 安装指南

## 环境要求

### 系统要求
- Python 3.7 或更高版本
- 可访问的 Kubernetes 集群
- 网络连接到 Kubernetes API Server

### Kubernetes 集群要求
- Kubernetes 1.18 或更高版本
- 支持 NodePort 服务类型
- 足够的资源配额创建 Pod

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd codespace-manager-python-k8s
```

### 2. 安装 Python 依赖
```bash
pip install -r requirements.txt
```

### 3. 配置 Kubernetes 连接

编辑 `config.json` 文件，设置正确的 Kubernetes API Server 地址：

```json
{
  "kubernetes": {
    "apiServer": "http://*************:8001/api/v1",
    "namespace": "codespace",
    "defaultImage": "python:bullseye"
  }
}
```

### 4. 验证连接

运行启动脚本进行环境检查：

```bash
python run.py
```

启动脚本会自动检查：
- Python 版本
- 依赖包
- 配置文件
- Kubernetes 连接

### 5. 启动应用

如果所有检查通过，应用会自动启动。您也可以直接运行：

```bash
python main.py
```

## 配置说明

### Kubernetes 配置

```json
{
  "kubernetes": {
    "apiServer": "http://*************:8001/api/v1",
    "namespace": "codespace",
    "defaultImage": "python:bullseye",
    "portRange": {
      "min": 30001,
      "max": 32767
    },
    "resources": {
      "requests": {
        "cpu": "100m",
        "memory": "256Mi"
      },
      "limits": {
        "cpu": "1000m",
        "memory": "2Gi"
      }
    }
  }
}
```

### AI 配置

```json
{
  "ai": {
    "provider": "openai",
    "apiKey": "your-api-key",
    "model": "qwen2.5-32b-instruct",
    "baseUrl": "https://gateway.chat.sensedeal.vip/v1"
  }
}
```

### 文件操作配置

```json
{
  "file_operations": {
    "max_file_size": "100MB",
    "supported_encodings": ["utf-8", "gbk", "ascii"],
    "chunk_size": 8192,
    "backup_enabled": true
  }
}
```

## 故障排除

### 常见问题

#### 1. Kubernetes 连接失败
- 检查 API Server 地址是否正确
- 确认网络连接正常
- 验证 Kubernetes 集群状态

#### 2. Pod 创建失败
- 检查命名空间是否存在
- 验证资源配额是否足够
- 查看 Kubernetes 事件日志

#### 3. 端口冲突
- 检查 NodePort 范围配置
- 确认端口没有被其他服务占用

#### 4. 依赖包安装失败
```bash
# 升级 pip
pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 日志查看

应用运行时的错误信息会直接显示在控制台。如需查看 Kubernetes 相关日志：

```bash
# 查看 Pod 日志
kubectl logs -n <namespace> <pod-name>

# 查看 Pod 事件
kubectl describe pod -n <namespace> <pod-name>

# 查看命名空间事件
kubectl get events -n <namespace>
```

## 开发环境设置

### 开发模式运行
```bash
# 设置环境变量
export PYTHON_ENV=development

# 启动应用
python main.py
```

### 调试模式
在代码中添加调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 升级指南

### 从旧版本升级
1. 备份用户数据：`cp -r data data.backup`
2. 更新代码：`git pull`
3. 更新依赖：`pip install -r requirements.txt --upgrade`
4. 检查配置文件是否需要更新
5. 重启应用

### 数据迁移
如果数据格式发生变化，请参考具体版本的迁移脚本。

## 性能优化

### 资源配置优化
根据实际使用情况调整 Pod 资源限制：

```json
{
  "resources": {
    "requests": {
      "cpu": "200m",
      "memory": "512Mi"
    },
    "limits": {
      "cpu": "2000m",
      "memory": "4Gi"
    }
  }
}
```

### 镜像优化
使用更轻量的基础镜像：
- `python:3.9-slim` 替代 `python:bullseye`
- 预构建包含常用工具的自定义镜像

### 网络优化
- 使用 LoadBalancer 类型的 Service（如果集群支持）
- 配置 Ingress 控制器进行流量管理
