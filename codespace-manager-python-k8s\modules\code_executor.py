import json
import time
from pathlib import Path
from datetime import datetime


class CodeExecutor:
    def __init__(self, k8s_manager):
        self.k8s_manager = k8s_manager
        self.config = self.load_config()
        self.supported_languages = {
            'python': {
                'extensions': ['.py'],
                'run_command': 'python3 {file}',
                'install_command': 'pip3 install {package}',
                'version_command': 'python3 --version'
            },
            'javascript': {
                'extensions': ['.js'],
                'run_command': 'node {file}',
                'install_command': 'npm install {package}',
                'version_command': 'node --version'
            },
            'java': {
                'extensions': ['.java'],
                'compile_command': 'javac {file}',
                'run_command': 'java {classname}',
                'version_command': 'java --version'
            },
            'cpp': {
                'extensions': ['.cpp', '.cc', '.cxx'],
                'compile_command': 'g++ -o {output} {file}',
                'run_command': './{output}',
                'version_command': 'g++ --version'
            },
            'c': {
                'extensions': ['.c'],
                'compile_command': 'gcc -o {output} {file}',
                'run_command': './{output}',
                'version_command': 'gcc --version'
            },
            'bash': {
                'extensions': ['.sh'],
                'run_command': 'bash {file}',
                'version_command': 'bash --version'
            }
        }

    def load_config(self):
        """加载配置文件"""
        config_file = Path.cwd() / 'config.json'
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def execute_command(self, user_id, command, working_dir='/home/<USER>', timeout=60):
        """在Pod中执行命令"""
        try:
            pod_info = self.k8s_manager.get_user_pod(user_id)
            if not pod_info:
                return {
                    'success': False,
                    'error': '用户Pod不存在',
                    'command': command,
                    'workingDir': working_dir,
                    'exitCode': -1
                }
            
            # 切换到工作目录并执行命令
            full_command = f'cd "{working_dir}" && {command}'
            
            start_time = time.time()
            result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'], 
                pod_info['namespace'], 
                full_command
            )
            execution_time = time.time() - start_time
            
            return {
                'success': result['success'],
                'command': command,
                'workingDir': working_dir,
                'exitCode': result.get('exitCode', 0 if result['success'] else 1),
                'stdout': result.get('output', ''),
                'stderr': result.get('error', ''),
                'executionTime': execution_time
            }
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error),
                'command': command,
                'workingDir': working_dir,
                'exitCode': -1
            }

    def run_file(self, user_id, file_path, args='', working_dir=None):
        """运行代码文件"""
        try:
            if working_dir is None:
                working_dir = '/home/<USER>'
            
            # 检测文件类型
            language = self.detect_language(file_path)
            if not language:
                return {
                    'success': False,
                    'error': f'不支持的文件类型: {file_path}',
                    'command': '',
                    'workingDir': working_dir,
                    'exitCode': -1
                }
            
            lang_config = self.supported_languages[language]
            
            # 编译步骤（如果需要）
            if 'compile_command' in lang_config:
                compile_result = self.compile_file(user_id, file_path, language, working_dir)
                if not compile_result['success']:
                    return compile_result
            
            # 构建运行命令
            if language == 'java':
                # Java需要特殊处理类名
                class_name = Path(file_path).stem
                run_command = lang_config['run_command'].format(classname=class_name)
            elif language in ['cpp', 'c']:
                # C/C++使用编译后的可执行文件
                output_name = Path(file_path).stem
                run_command = lang_config['run_command'].format(output=output_name)
            else:
                run_command = lang_config['run_command'].format(file=file_path)
            
            # 添加参数
            if args:
                run_command += f' {args}'
            
            # 执行文件
            return self.execute_command(user_id, run_command, working_dir)
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error),
                'command': '',
                'workingDir': working_dir or '/home/<USER>',
                'exitCode': -1
            }

    def compile_file(self, user_id, file_path, language, working_dir):
        """编译代码文件"""
        try:
            lang_config = self.supported_languages[language]
            
            if language == 'java':
                compile_command = lang_config['compile_command'].format(file=file_path)
            elif language in ['cpp', 'c']:
                output_name = Path(file_path).stem
                compile_command = lang_config['compile_command'].format(
                    output=output_name, 
                    file=file_path
                )
            else:
                return {'success': True, 'message': '无需编译'}
            
            # 执行编译
            result = self.execute_command(user_id, compile_command, working_dir)
            
            if result['success']:
                return {
                    'success': True,
                    'message': '编译成功',
                    'command': compile_command,
                    'workingDir': working_dir
                }
            else:
                return {
                    'success': False,
                    'error': f'编译失败: {result["stderr"]}',
                    'command': compile_command,
                    'workingDir': working_dir,
                    'exitCode': result['exitCode']
                }
                
        except Exception as error:
            return {
                'success': False,
                'error': str(error),
                'command': '',
                'workingDir': working_dir,
                'exitCode': -1
            }

    def detect_language(self, file_path):
        """检测文件的编程语言"""
        file_ext = Path(file_path).suffix.lower()
        
        for language, config in self.supported_languages.items():
            if file_ext in config['extensions']:
                return language
        
        return None

    def install_package(self, user_id, package_manager, package_name):
        """安装软件包"""
        try:
            commands = {
                'pip': f'pip3 install {package_name}',
                'npm': f'npm install {package_name}',
                'apt': f'apt update && apt install -y {package_name}'
            }
            
            if package_manager not in commands:
                return {
                    'success': False,
                    'error': f'不支持的包管理器: {package_manager}',
                    'command': '',
                    'workingDir': '/home/<USER>',
                    'exitCode': -1
                }
            
            command = commands[package_manager]
            
            # 对于apt，需要root权限
            if package_manager == 'apt':
                # 在K8s环境中，容器通常以root运行
                pass
            
            return self.execute_command(user_id, command, '/home/<USER>', timeout=300)
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error),
                'command': '',
                'workingDir': '/home/<USER>',
                'exitCode': -1
            }

    def get_system_info(self, user_id):
        """获取系统信息"""
        try:
            info_commands = {
                'python': 'python3 --version',
                'node': 'node --version',
                'java': 'java --version',
                'gcc': 'gcc --version | head -1',
                'git': 'git --version',
                'os': 'cat /etc/os-release | grep PRETTY_NAME',
                'memory': 'free -h',
                'disk': 'df -h /home/<USER>'
            }
            
            system_info = {}
            
            for name, command in info_commands.items():
                result = self.execute_command(user_id, command, '/home/<USER>', timeout=10)
                system_info[name] = {
                    'available': result['success'],
                    'output': result['stdout'].strip() if result['success'] else result.get('stderr', 'Not available')
                }
            
            return {
                'success': True,
                'systemInfo': system_info
            }
            
        except Exception as error:
            return {
                'success': False,
                'error': str(error)
            }
