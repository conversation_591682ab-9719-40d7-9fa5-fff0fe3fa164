# 主程序的菜单功能扩展
# 这个文件包含了主程序中的各种菜单功能

from colorama import Fore, Style
from inquirer import prompt, List, Text, Password, Confirm, Editor
import tempfile
import os


class MenuMixin:
    """菜单功能混入类"""
    
    def show_file_menu(self):
        """显示文件菜单"""
        questions = [
            List('action',
                 message='选择文件操作:',
                 choices=[
                     ('📋 列出文件', 'list'),
                     ('👀 查看文件', 'read'),
                     ('✏️ 编辑文件', 'write'),
                     ('🔄 替换内容', 'replace'),
                     ('📁 创建目录', 'mkdir'),
                     ('🗑️ 删除文件', 'delete'),
                     ('🔍 搜索文件', 'search'),
                     ('ℹ️ 文件信息', 'info'),
                     ('🔙 返回主菜单', 'back')
                 ])
        ]

        answers = prompt(questions)
        if not answers:
            return

        action = answers['action']

        if action == 'list':
            self.list_files()
        elif action == 'read':
            self.read_file()
        elif action == 'write':
            self.write_file()
        elif action == 'replace':
            self.replace_file_content()
        elif action == 'mkdir':
            self.create_directory()
        elif action == 'delete':
            self.delete_file()
        elif action == 'search':
            self.search_files()
        elif action == 'info':
            self.show_file_info()
        elif action == 'back':
            return

    def list_files(self):
        """列出文件"""
        questions = [
            Text('path', message='目录路径:', default='/home/<USER>')
        ]

        answers = prompt(questions)
        if not answers:
            return

        path = answers['path']

        print(f"{Fore.YELLOW}正在列出文件...")
        result = self.file_manager.list_files(self.current_user['id'], path)

        if result['success']:
            print(f"{Fore.GREEN}\n📁 目录: {result['path']}")
            if len(result['files']) == 0:
                print(f"{Fore.LIGHTBLACK_EX}目录为空")
            else:
                for file in result['files']:
                    icon = '📁' if file['isDirectory'] else '📄'
                    color = Fore.BLUE if file['isDirectory'] else Fore.WHITE
                    print(f"{color}{icon} {file['name']} ({file['size']}) {file['permissions']}")
        else:
            print(f"{Fore.RED}❌ 列出文件失败: {result['error']}")

        self.press_enter_to_continue()

    def read_file(self):
        """读取文件"""
        questions = [
            Text('file_path', message='文件路径:', validate=lambda _, x: x.strip() != '' or '文件路径不能为空')
        ]

        answers = prompt(questions)
        if not answers:
            return

        file_path = answers['file_path']

        print(f"{Fore.YELLOW}正在读取文件...")
        result = self.file_manager.read_file(self.current_user['id'], file_path)

        if result['success']:
            print(f"{Fore.GREEN}\n📄 文件内容: {result['path']}")
            print(f"{Fore.LIGHTBLACK_EX}编码: {result['encoding']}, 大小: {result['size']} bytes")
            print(f"{Fore.WHITE}{'─' * 50}")
            print(result['content'])
            print(f"{Fore.WHITE}{'─' * 50}")
        else:
            print(f"{Fore.RED}❌ 读取文件失败: {result['error']}")

        self.press_enter_to_continue()

    def write_file(self):
        """写入文件"""
        questions = [
            Text('file_path', message='文件路径:', validate=lambda _, x: x.strip() != '' or '文件路径不能为空')
        ]

        answers = prompt(questions)
        if not answers:
            return

        file_path = answers['file_path']

        # 检查文件是否存在，如果存在则读取现有内容
        existing_content = ""
        read_result = self.file_manager.read_file(self.current_user['id'], file_path)
        if read_result['success']:
            existing_content = read_result['content']
            print(f"{Fore.CYAN}📄 文件已存在，将在现有内容基础上编辑")

        # 使用临时文件进行编辑
        with tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(existing_content)
            temp_file_path = temp_file.name

        try:
            # 打开默认编辑器
            import subprocess
            editor = os.environ.get('EDITOR', 'notepad' if os.name == 'nt' else 'nano')
            subprocess.call([editor, temp_file_path])

            # 读取编辑后的内容
            with open(temp_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            print(f"{Fore.YELLOW}正在写入文件...")
            result = self.file_manager.write_file(self.current_user['id'], file_path, content)

            if result['success']:
                print(f"{Fore.GREEN}✅ 文件写入成功: {result['path']}")
                print(f"{Fore.LIGHTBLACK_EX}文件大小: {result['size']} bytes")
            else:
                print(f"{Fore.RED}❌ 写入文件失败: {result['error']}")
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)

        self.press_enter_to_continue()

    def replace_file_content(self):
        """替换文件内容"""
        questions = [
            Text('file_path', message='文件路径:', validate=lambda _, x: x.strip() != '' or '文件路径不能为空'),
            Text('old_content', message='要替换的内容:', validate=lambda _, x: x.strip() != '' or '内容不能为空'),
            Text('new_content', message='新内容:'),
            Confirm('regex', message='使用正则表达式?', default=False),
            Confirm('global_replace', message='替换所有匹配项?', default=False)
        ]

        answers = prompt(questions)
        if not answers:
            return

        file_path = answers['file_path']
        old_content = answers['old_content']
        new_content = answers['new_content']
        regex = answers['regex']
        global_replace = answers['global_replace']

        print(f"{Fore.YELLOW}正在替换文件内容...")
        result = self.file_manager.replace_file_content(
            self.current_user['id'], 
            file_path, 
            old_content, 
            new_content, 
            regex, 
            global_replace
        )

        if result['success']:
            if result['changed']:
                print(f"{Fore.GREEN}✅ 内容替换成功: {result['path']}")
                print(f"{Fore.LIGHTBLACK_EX}原大小: {result['original_size']} bytes")
                print(f"{Fore.LIGHTBLACK_EX}新大小: {result['new_size']} bytes")
            else:
                print(f"{Fore.YELLOW}⚠️ {result['message']}")
        else:
            print(f"{Fore.RED}❌ 替换失败: {result['error']}")

        self.press_enter_to_continue()

    def create_directory(self):
        """创建目录"""
        questions = [
            Text('dir_path', message='目录路径:', validate=lambda _, x: x.strip() != '' or '目录路径不能为空')
        ]

        answers = prompt(questions)
        if not answers:
            return

        dir_path = answers['dir_path']
        if not dir_path.startswith('/'):
            dir_path = f'/home/<USER>/{dir_path}'

        print(f"{Fore.YELLOW}正在创建目录...")
        result = self.file_manager.create_directory(self.current_user['id'], dir_path)

        if result['success']:
            print(f"{Fore.GREEN}✅ 目录创建成功: {result['path']}")
        else:
            print(f"{Fore.RED}❌ 创建目录失败: {result['error']}")

        self.press_enter_to_continue()

    def delete_file(self):
        """删除文件"""
        questions = [
            Text('file_path', message='文件路径:', validate=lambda _, x: x.strip() != '' or '文件路径不能为空'),
            Confirm('confirm', message='确认删除文件?', default=False)
        ]

        answers = prompt(questions)
        if not answers or not answers['confirm']:
            return

        file_path = answers['file_path']

        print(f"{Fore.YELLOW}正在删除文件...")
        result = self.file_manager.delete_file(self.current_user['id'], file_path)

        if result['success']:
            print(f"{Fore.GREEN}✅ 文件删除成功: {result['path']}")
        else:
            print(f"{Fore.RED}❌ 删除文件失败: {result['error']}")

        self.press_enter_to_continue()

    def search_files(self):
        """搜索文件"""
        questions = [
            Text('pattern', message='搜索内容:', validate=lambda _, x: x.strip() != '' or '搜索内容不能为空'),
            Text('path', message='搜索路径:', default='/home/<USER>'),
            Text('file_pattern', message='文件模式:', default='*')
        ]

        answers = prompt(questions)
        if not answers:
            return

        pattern = answers['pattern']
        path = answers['path']
        file_pattern = answers['file_pattern']

        print(f"{Fore.YELLOW}正在搜索文件...")
        result = self.file_manager.search_files(self.current_user['id'], pattern, path, file_pattern)

        if result['success']:
            print(f"{Fore.GREEN}\n🔍 搜索结果:")
            print(f"{Fore.LIGHTBLACK_EX}模式: {result['pattern']}")
            print(f"{Fore.LIGHTBLACK_EX}路径: {result['path']}")
            print(f"{Fore.LIGHTBLACK_EX}找到 {result['count']} 个文件:")
            
            for file_path in result['files']:
                print(f"{Fore.WHITE}📄 {file_path}")
        else:
            print(f"{Fore.RED}❌ 搜索失败: {result['error']}")

        self.press_enter_to_continue()

    def show_file_info(self):
        """显示文件信息"""
        questions = [
            Text('file_path', message='文件路径:', validate=lambda _, x: x.strip() != '' or '文件路径不能为空')
        ]

        answers = prompt(questions)
        if not answers:
            return

        file_path = answers['file_path']

        print(f"{Fore.YELLOW}正在获取文件信息...")
        result = self.file_manager.get_file_info(self.current_user['id'], file_path)

        if result['success']:
            info = result['file_info']
            print(f"{Fore.GREEN}\nℹ️ 文件信息:")
            print(f"{Fore.CYAN}路径: {info['path']}")
            print(f"{Fore.CYAN}大小: {info.get('size_human', 'Unknown')}")
            print(f"{Fore.CYAN}访问时间: {info.get('access_time', 'Unknown')}")
            print(f"{Fore.CYAN}修改时间: {info.get('modify_time', 'Unknown')}")
            print(f"{Fore.CYAN}状态改变时间: {info.get('change_time', 'Unknown')}")
        else:
            print(f"{Fore.RED}❌ 获取文件信息失败: {result['error']}")

        self.press_enter_to_continue()
