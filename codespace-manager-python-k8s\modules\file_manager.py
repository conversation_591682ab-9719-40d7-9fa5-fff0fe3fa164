import json
import base64
import re
import os
import tempfile
from pathlib import Path
from datetime import datetime


class FileManager:
    def __init__(self, k8s_manager):
        self.k8s_manager = k8s_manager
        self.config = self.load_config()
        self.max_file_size = self.parse_size(self.config['file_operations']['max_file_size'])
        self.chunk_size = self.config['file_operations']['chunk_size']
        self.supported_encodings = self.config['file_operations']['supported_encodings']
        self.backup_enabled = self.config['file_operations']['backup_enabled']

    def load_config(self):
        """加载配置文件"""
        config_file = Path.cwd() / 'config.json'
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def parse_size(self, size_str):
        """解析文件大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)

    def list_files(self, user_id, path='/home/<USER>'):
        """列出Pod内指定路径的文件"""
        try:
            pod_info = self.k8s_manager.get_user_pod(user_id)
            if not pod_info:
                return {'success': False, 'error': '用户Pod不存在'}
            
            # 执行ls命令获取文件列表
            command = f'ls -la "{path}" 2>/dev/null || echo "PATH_NOT_FOUND"'
            result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'], 
                pod_info['namespace'], 
                command
            )
            
            if not result['success']:
                return {'success': False, 'error': f'无法访问路径: {path}'}
            
            output = result['output']
            if 'PATH_NOT_FOUND' in output:
                return {'success': False, 'error': f'路径不存在: {path}'}
            
            # 解析ls输出
            files = self.parse_ls_output(output)
            
            return {
                'success': True,
                'path': path,
                'files': files
            }
        except Exception as error:
            return {'success': False, 'error': str(error)}

    def parse_ls_output(self, output):
        """解析ls命令输出"""
        files = []
        lines = output.strip().split('\n')
        
        for line in lines[1:]:  # 跳过第一行总计信息
            if not line.strip():
                continue
                
            parts = line.split()
            if len(parts) < 9:
                continue
            
            permissions = parts[0]
            size = parts[4]
            name = ' '.join(parts[8:])
            
            # 跳过当前目录和父目录
            if name in ['.', '..']:
                continue
            
            files.append({
                'name': name,
                'size': size,
                'permissions': permissions,
                'isDirectory': permissions.startswith('d'),
                'isFile': not permissions.startswith('d')
            })
        
        return files

    def read_file(self, user_id, file_path, encoding='utf-8'):
        """读取Pod内文件内容"""
        try:
            pod_info = self.k8s_manager.get_user_pod(user_id)
            if not pod_info:
                return {'success': False, 'error': '用户Pod不存在'}
            
            # 检查文件是否存在
            check_command = f'test -f "{file_path}" && echo "EXISTS" || echo "NOT_EXISTS"'
            check_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'], 
                pod_info['namespace'], 
                check_command
            )
            
            if not check_result['success'] or 'NOT_EXISTS' in check_result['output']:
                return {'success': False, 'error': f'文件不存在: {file_path}'}
            
            # 检查文件大小
            size_command = f'stat -c%s "{file_path}"'
            size_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'], 
                pod_info['namespace'], 
                size_command
            )
            
            if size_result['success']:
                try:
                    file_size = int(size_result['output'].strip())
                    if file_size > self.max_file_size:
                        return {
                            'success': False, 
                            'error': f'文件过大: {file_size} bytes (最大: {self.max_file_size} bytes)'
                        }
                except ValueError:
                    pass
            
            # 使用base64编码读取文件内容以确保二进制安全
            read_command = f'base64 "{file_path}"'
            read_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'], 
                pod_info['namespace'], 
                read_command
            )
            
            if not read_result['success']:
                return {'success': False, 'error': f'无法读取文件: {file_path}'}
            
            try:
                # 解码base64内容
                encoded_content = read_result['output'].strip()
                binary_content = base64.b64decode(encoded_content)
                
                # 尝试使用指定编码解码
                content = self.decode_content(binary_content, encoding)
                
                return {
                    'success': True,
                    'path': file_path,
                    'content': content,
                    'encoding': encoding,
                    'size': len(binary_content)
                }
            except Exception as decode_error:
                return {'success': False, 'error': f'文件解码失败: {decode_error}'}
                
        except Exception as error:
            return {'success': False, 'error': str(error)}

    def decode_content(self, binary_content, preferred_encoding='utf-8'):
        """智能解码文件内容"""
        encodings_to_try = [preferred_encoding] + [
            enc for enc in self.supported_encodings if enc != preferred_encoding
        ]
        
        for encoding in encodings_to_try:
            try:
                return binary_content.decode(encoding)
            except UnicodeDecodeError:
                continue
        
        # 如果所有编码都失败，使用错误处理
        return binary_content.decode('utf-8', errors='replace')

    def write_file(self, user_id, file_path, content, encoding='utf-8', create_backup=None):
        """写入文件到Pod内 - 使用规范的方式，不使用echo命令"""
        try:
            pod_info = self.k8s_manager.get_user_pod(user_id)
            if not pod_info:
                return {'success': False, 'error': '用户Pod不存在'}
            
            # 检查文件路径安全性
            if not self.is_safe_path(file_path):
                return {'success': False, 'error': f'不安全的文件路径: {file_path}'}
            
            # 确定是否需要备份
            if create_backup is None:
                create_backup = self.backup_enabled
            
            # 创建备份（如果文件存在且启用备份）
            if create_backup:
                backup_result = self.create_backup(user_id, file_path)
                if not backup_result['success'] and backup_result.get('file_exists', False):
                    print(f"警告: 备份创建失败: {backup_result['error']}")
            
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path:
                mkdir_command = f'mkdir -p "{dir_path}"'
                self.k8s_manager.execute_command_in_pod(
                    pod_info['podName'], 
                    pod_info['namespace'], 
                    mkdir_command
                )
            
            # 编码内容
            try:
                binary_content = content.encode(encoding)
            except UnicodeEncodeError as e:
                return {'success': False, 'error': f'内容编码失败: {e}'}
            
            # 检查内容大小
            if len(binary_content) > self.max_file_size:
                return {
                    'success': False, 
                    'error': f'内容过大: {len(binary_content)} bytes (最大: {self.max_file_size} bytes)'
                }
            
            # 使用base64编码确保二进制安全传输
            encoded_content = base64.b64encode(binary_content).decode('ascii')
            
            # 使用临时文件方式写入，避免echo命令的限制
            temp_file = f'/tmp/codespace_write_{int(datetime.now().timestamp())}'
            
            # 分块写入大文件
            if len(encoded_content) > self.chunk_size:
                return self.write_large_file(pod_info, file_path, encoded_content, temp_file)
            else:
                return self.write_small_file(pod_info, file_path, encoded_content, temp_file)
                
        except Exception as error:
            return {'success': False, 'error': str(error)}

    def write_small_file(self, pod_info, file_path, encoded_content, temp_file):
        """写入小文件"""
        try:
            # 将base64内容写入临时文件
            write_command = f'printf "{encoded_content}" > "{temp_file}"'
            write_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                write_command
            )

            if not write_result['success']:
                return {'success': False, 'error': f'写入临时文件失败: {write_result["error"]}'}

            # 解码并移动到目标位置
            decode_command = f'base64 -d "{temp_file}" > "{file_path}" && rm "{temp_file}"'
            decode_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                decode_command
            )

            if not decode_result['success']:
                # 清理临时文件
                self.k8s_manager.execute_command_in_pod(
                    pod_info['podName'],
                    pod_info['namespace'],
                    f'rm -f "{temp_file}"'
                )
                return {'success': False, 'error': f'解码文件失败: {decode_result["error"]}'}

            return {
                'success': True,
                'path': file_path,
                'size': len(base64.b64decode(encoded_content))
            }

        except Exception as error:
            return {'success': False, 'error': str(error)}

    def write_large_file(self, pod_info, file_path, encoded_content, temp_file):
        """分块写入大文件"""
        try:
            # 清空临时文件
            clear_command = f'> "{temp_file}"'
            self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                clear_command
            )

            # 分块写入
            chunk_size = self.chunk_size
            for i in range(0, len(encoded_content), chunk_size):
                chunk = encoded_content[i:i + chunk_size]
                append_command = f'printf "{chunk}" >> "{temp_file}"'

                append_result = self.k8s_manager.execute_command_in_pod(
                    pod_info['podName'],
                    pod_info['namespace'],
                    append_command
                )

                if not append_result['success']:
                    # 清理临时文件
                    self.k8s_manager.execute_command_in_pod(
                        pod_info['podName'],
                        pod_info['namespace'],
                        f'rm -f "{temp_file}"'
                    )
                    return {'success': False, 'error': f'写入块失败: {append_result["error"]}'}

            # 解码并移动到目标位置
            decode_command = f'base64 -d "{temp_file}" > "{file_path}" && rm "{temp_file}"'
            decode_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                decode_command
            )

            if not decode_result['success']:
                # 清理临时文件
                self.k8s_manager.execute_command_in_pod(
                    pod_info['podName'],
                    pod_info['namespace'],
                    f'rm -f "{temp_file}"'
                )
                return {'success': False, 'error': f'解码大文件失败: {decode_result["error"]}'}

            return {
                'success': True,
                'path': file_path,
                'size': len(base64.b64decode(encoded_content))
            }

        except Exception as error:
            return {'success': False, 'error': str(error)}

    def replace_file_content(self, user_id, file_path, old_content, new_content, regex=False, global_replace=False):
        """替换文件内容 - 新增功能"""
        try:
            # 首先读取文件
            read_result = self.read_file(user_id, file_path)
            if not read_result['success']:
                return read_result

            original_content = read_result['content']

            # 执行替换
            if regex:
                # 正则表达式替换
                flags = re.MULTILINE | re.DOTALL
                if global_replace:
                    modified_content = re.sub(old_content, new_content, original_content, flags=flags)
                else:
                    modified_content = re.sub(old_content, new_content, original_content, count=1, flags=flags)
            else:
                # 字符串替换
                if global_replace:
                    modified_content = original_content.replace(old_content, new_content)
                else:
                    modified_content = original_content.replace(old_content, new_content, 1)

            # 检查是否有变化
            if modified_content == original_content:
                return {
                    'success': True,
                    'path': file_path,
                    'changed': False,
                    'message': '没有找到匹配的内容进行替换'
                }

            # 写入修改后的内容
            write_result = self.write_file(user_id, file_path, modified_content, create_backup=True)
            if write_result['success']:
                return {
                    'success': True,
                    'path': file_path,
                    'changed': True,
                    'original_size': len(original_content),
                    'new_size': len(modified_content)
                }
            else:
                return write_result

        except Exception as error:
            return {'success': False, 'error': str(error)}

    def create_backup(self, user_id, file_path):
        """创建文件备份"""
        try:
            pod_info = self.k8s_manager.get_user_pod(user_id)
            if not pod_info:
                return {'success': False, 'error': '用户Pod不存在'}

            # 检查文件是否存在
            check_command = f'test -f "{file_path}" && echo "EXISTS" || echo "NOT_EXISTS"'
            check_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                check_command
            )

            if 'NOT_EXISTS' in check_result['output']:
                return {'success': True, 'message': '文件不存在，无需备份', 'file_exists': False}

            # 创建备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"{file_path}.backup_{timestamp}"

            # 复制文件
            copy_command = f'cp "{file_path}" "{backup_path}"'
            copy_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                copy_command
            )

            if copy_result['success']:
                return {
                    'success': True,
                    'backup_path': backup_path,
                    'file_exists': True
                }
            else:
                return {
                    'success': False,
                    'error': f'备份失败: {copy_result["error"]}',
                    'file_exists': True
                }

        except Exception as error:
            return {'success': False, 'error': str(error), 'file_exists': True}

    def is_safe_path(self, file_path):
        """检查文件路径安全性"""
        # 检查是否包含危险路径
        forbidden_paths = self.config['security']['forbidden_paths']
        for forbidden in forbidden_paths:
            if file_path.startswith(forbidden):
                return False

        # 检查是否包含路径遍历
        if '..' in file_path or file_path.startswith('/'):
            # 允许绝对路径，但要在工作目录内
            if not file_path.startswith('/home/<USER>'):
                return False

        return True

    def delete_file(self, user_id, file_path):
        """删除文件"""
        try:
            pod_info = self.k8s_manager.get_user_pod(user_id)
            if not pod_info:
                return {'success': False, 'error': '用户Pod不存在'}

            if not self.is_safe_path(file_path):
                return {'success': False, 'error': f'不安全的文件路径: {file_path}'}

            # 创建备份（如果启用）
            if self.backup_enabled:
                backup_result = self.create_backup(user_id, file_path)
                if not backup_result['success'] and backup_result.get('file_exists', False):
                    print(f"警告: 删除前备份失败: {backup_result['error']}")

            # 删除文件
            delete_command = f'rm -f "{file_path}"'
            delete_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                delete_command
            )

            if delete_result['success']:
                return {
                    'success': True,
                    'path': file_path,
                    'message': '文件删除成功'
                }
            else:
                return {'success': False, 'error': f'删除文件失败: {delete_result["error"]}'}

        except Exception as error:
            return {'success': False, 'error': str(error)}

    def create_directory(self, user_id, dir_path):
        """创建目录"""
        try:
            pod_info = self.k8s_manager.get_user_pod(user_id)
            if not pod_info:
                return {'success': False, 'error': '用户Pod不存在'}

            if not self.is_safe_path(dir_path):
                return {'success': False, 'error': f'不安全的目录路径: {dir_path}'}

            # 创建目录
            mkdir_command = f'mkdir -p "{dir_path}"'
            mkdir_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                mkdir_command
            )

            if mkdir_result['success']:
                return {
                    'success': True,
                    'path': dir_path,
                    'message': '目录创建成功'
                }
            else:
                return {'success': False, 'error': f'创建目录失败: {mkdir_result["error"]}'}

        except Exception as error:
            return {'success': False, 'error': str(error)}

    def search_files(self, user_id, pattern, path='/home/<USER>', file_pattern='*'):
        """在文件中搜索内容"""
        try:
            pod_info = self.k8s_manager.get_user_pod(user_id)
            if not pod_info:
                return {'success': False, 'error': '用户Pod不存在'}

            # 使用grep搜索
            search_command = f'find "{path}" -name "{file_pattern}" -type f -exec grep -l "{pattern}" {{}} \\; 2>/dev/null'
            search_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                search_command
            )

            if search_result['success']:
                files = [f.strip() for f in search_result['output'].split('\n') if f.strip()]
                return {
                    'success': True,
                    'pattern': pattern,
                    'path': path,
                    'files': files,
                    'count': len(files)
                }
            else:
                return {'success': False, 'error': f'搜索失败: {search_result["error"]}'}

        except Exception as error:
            return {'success': False, 'error': str(error)}

    def get_file_info(self, user_id, file_path):
        """获取文件详细信息"""
        try:
            pod_info = self.k8s_manager.get_user_pod(user_id)
            if not pod_info:
                return {'success': False, 'error': '用户Pod不存在'}

            # 获取文件统计信息
            stat_command = f'stat "{file_path}" 2>/dev/null || echo "FILE_NOT_FOUND"'
            stat_result = self.k8s_manager.execute_command_in_pod(
                pod_info['podName'],
                pod_info['namespace'],
                stat_command
            )

            if not stat_result['success'] or 'FILE_NOT_FOUND' in stat_result['output']:
                return {'success': False, 'error': f'文件不存在: {file_path}'}

            # 解析stat输出
            info = self.parse_stat_output(stat_result['output'])
            info['path'] = file_path

            return {
                'success': True,
                'file_info': info
            }

        except Exception as error:
            return {'success': False, 'error': str(error)}

    def parse_stat_output(self, output):
        """解析stat命令输出"""
        info = {}
        lines = output.strip().split('\n')

        for line in lines:
            if 'Size:' in line:
                parts = line.split()
                info['size'] = int(parts[1])
                info['size_human'] = self.format_size(info['size'])
            elif 'Access:' in line and 'Uid:' not in line:
                info['access_time'] = line.split('Access: ')[1].split('.')[0]
            elif 'Modify:' in line:
                info['modify_time'] = line.split('Modify: ')[1].split('.')[0]
            elif 'Change:' in line:
                info['change_time'] = line.split('Change: ')[1].split('.')[0]

        return info

    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
