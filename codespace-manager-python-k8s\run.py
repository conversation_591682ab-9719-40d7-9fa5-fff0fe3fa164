#!/usr/bin/env python3
"""
CodeSpace Manager - Kubernetes 版本启动脚本

这个脚本用于启动 CodeSpace Manager 应用程序。
它会检查依赖项、配置环境，然后启动主应用程序。
"""

import sys
import os
import subprocess
from pathlib import Path
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print(f"{Fore.RED}❌ 需要Python 3.7或更高版本")
        print(f"{Fore.YELLOW}当前版本: {sys.version}")
        return False
    
    print(f"{Fore.GREEN}✅ Python版本检查通过: {sys.version.split()[0]}")
    return True


def check_dependencies():
    """检查依赖项"""
    required_packages = [
        'kubernetes',
        'bcrypt',
        'colorama',
        'inquirer',
        'requests',
        'pyyaml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"{Fore.GREEN}✅ {package}")
        except ImportError:
            print(f"{Fore.RED}❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n{Fore.YELLOW}缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        
        print(f"\n{Fore.CYAN}请运行以下命令安装依赖:")
        print(f"{Fore.WHITE}pip install -r requirements.txt")
        return False
    
    return True


def check_config():
    """检查配置文件"""
    config_file = Path(__file__).parent / 'config.json'
    
    if not config_file.exists():
        print(f"{Fore.RED}❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的配置项
        required_keys = ['ai', 'kubernetes', 'paths']
        for key in required_keys:
            if key not in config:
                print(f"{Fore.RED}❌ 配置文件缺少必要项: {key}")
                return False
        
        print(f"{Fore.GREEN}✅ 配置文件检查通过")
        return True
        
    except json.JSONDecodeError as e:
        print(f"{Fore.RED}❌ 配置文件格式错误: {e}")
        return False
    except Exception as e:
        print(f"{Fore.RED}❌ 读取配置文件失败: {e}")
        return False


def check_kubernetes_connection():
    """检查Kubernetes连接"""
    try:
        import requests
        import json
        
        config_file = Path(__file__).parent / 'config.json'
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        api_server = config['kubernetes']['apiServer']
        
        # 尝试连接到Kubernetes API
        response = requests.get(f"{api_server}/version", timeout=5)
        
        if response.status_code == 200:
            print(f"{Fore.GREEN}✅ Kubernetes连接正常")
            return True
        else:
            print(f"{Fore.YELLOW}⚠️ Kubernetes连接异常: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"{Fore.YELLOW}⚠️ 无法连接到Kubernetes: {e}")
        return False
    except Exception as e:
        print(f"{Fore.RED}❌ Kubernetes连接检查失败: {e}")
        return False


def create_data_directories():
    """创建必要的数据目录"""
    directories = ['data', 'k8s']
    
    for dir_name in directories:
        dir_path = Path(__file__).parent / dir_name
        dir_path.mkdir(exist_ok=True)
        print(f"{Fore.GREEN}✅ 目录已准备: {dir_name}")


def show_banner():
    """显示启动横幅"""
    banner = f"""
{Fore.BLUE}{Style.BRIGHT}
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🚀 CodeSpace Manager - Kubernetes Edition                ║
║                                                              ║
║    基于 Kubernetes 的智能容器管理系统                        ║
║    支持多用户、文件管理、代码执行、AI助手                     ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}
"""
    print(banner)


def main():
    """主函数"""
    show_banner()
    
    print(f"{Fore.CYAN}正在进行启动前检查...\n")
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    print()
    
    # 检查依赖项
    print(f"{Fore.CYAN}检查依赖项:")
    if not check_dependencies():
        sys.exit(1)
    
    print()
    
    # 检查配置文件
    print(f"{Fore.CYAN}检查配置文件:")
    if not check_config():
        sys.exit(1)
    
    print()
    
    # 检查Kubernetes连接
    print(f"{Fore.CYAN}检查Kubernetes连接:")
    k8s_ok = check_kubernetes_connection()
    
    print()
    
    # 创建数据目录
    print(f"{Fore.CYAN}准备数据目录:")
    create_data_directories()
    
    print()
    
    if not k8s_ok:
        print(f"{Fore.YELLOW}⚠️ Kubernetes连接异常，但应用仍可启动")
        print(f"{Fore.YELLOW}请检查Kubernetes集群状态和配置")
        
        # 询问是否继续
        try:
            choice = input(f"{Fore.CYAN}是否继续启动应用? (y/N): ").strip().lower()
            if choice not in ['y', 'yes']:
                print(f"{Fore.BLUE}启动已取消")
                sys.exit(0)
        except KeyboardInterrupt:
            print(f"\n{Fore.BLUE}启动已取消")
            sys.exit(0)
    
    print(f"{Fore.GREEN}✅ 所有检查完成，正在启动应用...\n")
    
    # 启动主应用程序
    try:
        from main import CodeSpaceManagerK8s
        app = CodeSpaceManagerK8s()
        app.start()
    except KeyboardInterrupt:
        print(f"\n{Fore.BLUE}应用已停止")
    except Exception as error:
        print(f"{Fore.RED}应用启动失败: {error}")
        sys.exit(1)


if __name__ == "__main__":
    main()
