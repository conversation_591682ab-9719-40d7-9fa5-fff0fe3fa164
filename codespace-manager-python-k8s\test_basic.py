#!/usr/bin/env python3
"""
基本功能测试脚本

这个脚本用于测试 CodeSpace Manager 的基本功能，
包括模块导入、配置加载、Kubernetes连接等。
"""

import sys
import json
from pathlib import Path
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)

# 添加模块路径
sys.path.append(str(Path(__file__).parent / 'modules'))


def test_imports():
    """测试模块导入"""
    print(f"{Fore.CYAN}测试模块导入...")
    
    try:
        from user_manager import UserManager
        print(f"{Fore.GREEN}✅ UserManager")
    except ImportError as e:
        print(f"{Fore.RED}❌ UserManager: {e}")
        return False
    
    try:
        from k8s_manager import K8sManager
        print(f"{Fore.GREEN}✅ K8sManager")
    except ImportError as e:
        print(f"{Fore.RED}❌ K8sManager: {e}")
        return False
    
    try:
        from file_manager import FileManager
        print(f"{Fore.GREEN}✅ FileManager")
    except ImportError as e:
        print(f"{Fore.RED}❌ FileManager: {e}")
        return False
    
    try:
        from code_executor import CodeExecutor
        print(f"{Fore.GREEN}✅ CodeExecutor")
    except ImportError as e:
        print(f"{Fore.RED}❌ CodeExecutor: {e}")
        return False
    
    try:
        from ai_manager import AIManager
        print(f"{Fore.GREEN}✅ AIManager")
    except ImportError as e:
        print(f"{Fore.RED}❌ AIManager: {e}")
        return False
    
    return True


def test_config():
    """测试配置文件"""
    print(f"\n{Fore.CYAN}测试配置文件...")
    
    config_file = Path(__file__).parent / 'config.json'
    
    if not config_file.exists():
        print(f"{Fore.RED}❌ 配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的配置项
        required_sections = ['ai', 'kubernetes', 'paths', 'file_operations', 'security']
        
        for section in required_sections:
            if section in config:
                print(f"{Fore.GREEN}✅ {section} 配置存在")
            else:
                print(f"{Fore.RED}❌ {section} 配置缺失")
                return False
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"{Fore.RED}❌ 配置文件格式错误: {e}")
        return False
    except Exception as e:
        print(f"{Fore.RED}❌ 读取配置文件失败: {e}")
        return False


def test_managers():
    """测试管理器初始化"""
    print(f"\n{Fore.CYAN}测试管理器初始化...")
    
    try:
        from user_manager import UserManager
        user_manager = UserManager()
        print(f"{Fore.GREEN}✅ UserManager 初始化成功")
    except Exception as e:
        print(f"{Fore.RED}❌ UserManager 初始化失败: {e}")
        return False
    
    try:
        from k8s_manager import K8sManager
        k8s_manager = K8sManager()
        print(f"{Fore.GREEN}✅ K8sManager 初始化成功")
    except Exception as e:
        print(f"{Fore.RED}❌ K8sManager 初始化失败: {e}")
        return False
    
    try:
        from file_manager import FileManager
        file_manager = FileManager(k8s_manager)
        print(f"{Fore.GREEN}✅ FileManager 初始化成功")
    except Exception as e:
        print(f"{Fore.RED}❌ FileManager 初始化失败: {e}")
        return False
    
    try:
        from code_executor import CodeExecutor
        code_executor = CodeExecutor(k8s_manager)
        print(f"{Fore.GREEN}✅ CodeExecutor 初始化成功")
    except Exception as e:
        print(f"{Fore.RED}❌ CodeExecutor 初始化失败: {e}")
        return False
    
    try:
        from ai_manager import AIManager
        ai_manager = AIManager()
        print(f"{Fore.GREEN}✅ AIManager 初始化成功")
    except Exception as e:
        print(f"{Fore.RED}❌ AIManager 初始化失败: {e}")
        return False
    
    return True


def test_kubernetes_connection():
    """测试Kubernetes连接"""
    print(f"\n{Fore.CYAN}测试Kubernetes连接...")
    
    try:
        import requests
        
        config_file = Path(__file__).parent / 'config.json'
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        api_server = config['kubernetes']['apiServer']
        
        # 尝试连接到Kubernetes API
        response = requests.get(f"{api_server}/version", timeout=5)
        
        if response.status_code == 200:
            print(f"{Fore.GREEN}✅ Kubernetes连接成功")
            version_info = response.json()
            print(f"{Fore.LIGHTBLACK_EX}   版本: {version_info.get('gitVersion', 'Unknown')}")
            return True
        else:
            print(f"{Fore.YELLOW}⚠️ Kubernetes连接异常: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"{Fore.YELLOW}⚠️ 无法连接到Kubernetes: {e}")
        return False
    except Exception as e:
        print(f"{Fore.RED}❌ Kubernetes连接测试失败: {e}")
        return False


def test_data_directories():
    """测试数据目录"""
    print(f"\n{Fore.CYAN}测试数据目录...")
    
    directories = ['data', 'k8s', 'docs']
    
    for dir_name in directories:
        dir_path = Path(__file__).parent / dir_name
        if dir_path.exists() and dir_path.is_dir():
            print(f"{Fore.GREEN}✅ {dir_name} 目录存在")
        else:
            print(f"{Fore.YELLOW}⚠️ {dir_name} 目录不存在")
    
    return True


def main():
    """主测试函数"""
    print(f"{Fore.BLUE}{Style.BRIGHT}")
    print("=" * 60)
    print("  CodeSpace Manager - Kubernetes 版本基本功能测试")
    print("=" * 60)
    print(f"{Style.RESET_ALL}")
    
    tests = [
        ("模块导入", test_imports),
        ("配置文件", test_config),
        ("管理器初始化", test_managers),
        ("Kubernetes连接", test_kubernetes_connection),
        ("数据目录", test_data_directories)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"{Fore.RED}❌ {test_name} 测试异常: {e}")
    
    print(f"\n{Fore.BLUE}{Style.BRIGHT}")
    print("=" * 60)
    print(f"  测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print(f"{Fore.GREEN}  🎉 所有测试通过！系统准备就绪。")
    elif passed >= total - 1:
        print(f"{Fore.YELLOW}  ⚠️ 大部分测试通过，系统基本可用。")
    else:
        print(f"{Fore.RED}  ❌ 多个测试失败，请检查配置和环境。")
    
    print("=" * 60)
    print(f"{Style.RESET_ALL}")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
